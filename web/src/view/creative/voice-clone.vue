<template>
  <div class="voice-clone-container">
    <el-tabs v-model="activeTab" type="card" class="voice-clone-tabs">
      <el-tab-pane label="RunningHub 人声克隆" name="runninghub">
        <RunningHubVoiceClone />
      </el-tab-pane>
      <el-tab-pane label="MiniMax 人声克隆" name="minimax">
        <MinimaxVoiceClone />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import RunningHubVoiceClone from './components/RunningHubVoiceClone.vue'
  import MinimaxVoiceClone from './components/MinimaxVoiceClone.vue'

  defineOptions({
    name: 'VoiceClone'
  })

  const activeTab = ref('runninghub')
</script>

<style scoped>
  .voice-clone-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .voice-clone-tabs {
    width: 100%;
  }

  :deep(.el-tabs__content) {
    padding: 0;
  }

  :deep(.el-tab-pane) {
    padding: 0;
  }
</style>
