<template>
  <div class="minimax-voice-clone">
    <el-card class="clone-form-card">
      <el-form ref="cloneForm" :model="formData" :rules="rules" label-width="120px" size="large">
        <!-- 文本输入 -->
        <el-form-item label="转换文本" prop="text">
          <el-input
            v-model="formData.text"
            type="textarea"
            :rows="4"
            placeholder="请输入要转换成语音的文本内容..."
            maxlength="5000"
            show-word-limit
            class="text-input"
          />
        </el-form-item>

        <!-- 音色选择 -->
        <el-form-item label="选择音色" prop="referenceAudioId">
          <div class="voice-selection">
            <el-cascader
              v-model="formData.referenceAudioPath"
              :options="voiceOptions"
              :props="cascaderProps"
              placeholder="请选择参考音色"
              style="width: 100%"
              filterable
              @change="handleVoiceSelect"
            />
            <div v-if="selectedVoice" class="selected-voice-preview">
              <div class="voice-info">
                <span class="voice-name">{{ selectedVoice.name }}</span>
                <span class="voice-category">{{ selectedVoice.categoryName }}</span>
              </div>
              <div class="voice-preview-controls">
                <el-button
                  :type="playingStates.get(selectedVoice.fileUrl) ? 'warning' : 'success'"
                  size="small"
                  @click="toggleAudioPlay(selectedVoice.fileUrl)"
                >
                  <el-icon v-if="playingStates.get(selectedVoice.fileUrl)"><VideoPause /></el-icon>
                  <el-icon v-else><VideoPlay /></el-icon>
                  {{ playingStates.get(selectedVoice.fileUrl) ? '暂停' : '播放' }}
                </el-button>
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 存放位置选择 -->
        <el-form-item label="存放位置" prop="targetCategoryId">
          <el-cascader
            v-model="formData.targetCategoryPath"
            :options="targetCategoryOptions"
            :props="cascaderProps"
            placeholder="请选择生成音频的存放分类"
            style="width: 100%"
            filterable
            @change="handleTargetCategorySelect"
          />
        </el-form-item>

        <!-- 高级设置 -->
        <el-form-item label="" prop="">
          <div class="advanced-settings-wrapper">
            <el-collapse v-model="advancedOpen">
              <el-collapse-item title="高级设置" name="advanced">
                <div class="advanced-settings-content">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="模型" label-width="120px">
                        <el-select v-model="formData.model" placeholder="选择模型" style="width: 100%">
                          <el-option label="speech-01" value="speech-01" />
                          <el-option label="speech-01-240228" value="speech-01-240228" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="音频格式" label-width="120px">
                        <el-select v-model="formData.format" placeholder="选择格式" style="width: 100%">
                          <el-option label="MP3" value="mp3" />
                          <el-option label="WAV" value="wav" />
                          <el-option label="PCM" value="pcm" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label-width="120px">
                        <template #label>
                          <span>语速</span>
                          <el-tooltip
                            content="控制语音播放速度。1.0为正常速度，小于1.0较慢，大于1.0较快"
                            placement="top"
                          >
                            <el-icon style="margin-left: 4px; color: var(--el-color-info)"><QuestionFilled /></el-icon>
                          </el-tooltip>
                        </template>
                        <div class="slider-container">
                          <el-slider
                            v-model="formData.speed"
                            :min="0.5"
                            :max="2.0"
                            :step="0.1"
                            show-tooltip
                            class="custom-slider"
                          />
                          <span class="slider-value">{{ formData.speed }}</span>
                        </div>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label-width="120px">
                        <template #label>
                          <span>音量</span>
                          <el-tooltip
                            content="控制音频音量大小。1.0为正常音量"
                            placement="top"
                          >
                            <el-icon style="margin-left: 4px; color: var(--el-color-info)"><QuestionFilled /></el-icon>
                          </el-tooltip>
                        </template>
                        <div class="slider-container">
                          <el-slider
                            v-model="formData.volume"
                            :min="0.1"
                            :max="2.0"
                            :step="0.1"
                            show-tooltip
                            class="custom-slider"
                          />
                          <span class="slider-value">{{ formData.volume }}</span>
                        </div>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label-width="120px">
                        <template #label>
                          <span>音调</span>
                          <el-tooltip
                            content="控制音频音调高低。0为正常音调，正值提高音调，负值降低音调"
                            placement="top"
                          >
                            <el-icon style="margin-left: 4px; color: var(--el-color-info)"><QuestionFilled /></el-icon>
                          </el-tooltip>
                        </template>
                        <div class="slider-container">
                          <el-slider
                            v-model="formData.pitch"
                            :min="-12"
                            :max="12"
                            :step="1"
                            show-tooltip
                            class="custom-slider"
                          />
                          <span class="slider-value">{{ formData.pitch }}</span>
                        </div>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="采样率" label-width="120px">
                        <el-select v-model="formData.audioSampleRate" placeholder="选择采样率" style="width: 100%">
                          <el-option label="16000 Hz" :value="16000" />
                          <el-option label="24000 Hz" :value="24000" />
                          <el-option label="32000 Hz" :value="32000" />
                          <el-option label="48000 Hz" :value="48000" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="比特率" label-width="120px">
                        <el-select v-model="formData.bitRate" placeholder="选择比特率" style="width: 100%">
                          <el-option label="64 kbps" :value="64000" />
                          <el-option label="128 kbps" :value="128000" />
                          <el-option label="192 kbps" :value="192000" />
                          <el-option label="256 kbps" :value="256000" />
                          <el-option label="320 kbps" :value="320000" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="submitClone" size="large">
            <el-icon><MagicStick /></el-icon>
            开始克隆
          </el-button>
          <el-button @click="resetForm" size="large">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 任务列表 -->
    <el-card class="task-list-card" v-if="tasks.length > 0">
      <template #header>
        <div class="card-header">
          <span>MiniMax 克隆任务</span>
          <el-button text @click="refreshTasks">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>

      <el-table :data="tasks" style="width: 100%">
        <el-table-column prop="text" label="文本内容" min-width="200">
          <template #default="scope">
            <div class="text-preview">{{ scope.row.text.substring(0, 50) }}...</div>
          </template>
        </el-table-column>
        <el-table-column prop="referenceAudioName" label="声源" width="150">
          <template #default="scope">
            <span v-if="scope.row.referenceAudioName">{{ scope.row.referenceAudioName }}</span>
            <span v-else class="text-gray-400">--</span>
          </template>
        </el-table-column>
        <el-table-column prop="voiceId" label="音色ID" width="150">
          <template #default="scope">
            <span v-if="scope.row.voiceId" class="voice-id">{{ scope.row.voiceId }}</span>
            <span v-else class="text-gray-400">--</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="时长" width="100">
          <template #default="scope">
            <span v-if="scope.row.duration">{{ formatDuration(scope.row.duration) }}</span>
            <span v-else class="text-gray-400">--</span>
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.startTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button
              v-if="scope.row.audioUrl"
              :type="playingStates.get(scope.row.audioUrl) ? 'warning' : 'success'"
              size="small"
              @click="toggleAudioPlay(scope.row.audioUrl)"
            >
              <el-icon v-if="playingStates.get(scope.row.audioUrl)"><VideoPause /></el-icon>
              <el-icon v-else><VideoPlay /></el-icon>
              {{ playingStates.get(scope.row.audioUrl) ? '暂停' : '播放' }}
            </el-button>
            <el-button v-if="scope.row.audioUrl" type="primary" size="small" @click="downloadAudio(scope.row)">
              <el-icon><Download /></el-icon>
              下载
            </el-button>
            <el-button
              v-if="scope.row.status === 'RUNNING' || scope.row.status === 'PENDING'"
              type="info"
              size="small"
              @click="checkTaskStatus(scope.row.taskId)"
            >
              <el-icon><Refresh /></el-icon>
              刷新状态
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage } from 'element-plus'
  import { MagicStick, Refresh, VideoPlay, VideoPause, Download, QuestionFilled } from '@element-plus/icons-vue'
  import { createMinimaxVoiceCloneTask, getMinimaxVoiceCloneTaskList, getMinimaxVoiceCloneTaskStatus } from '@/api/ai/minimaxVoiceClone'
  import { getMusicList } from '@/api/media/music'
  import { getCategoryList } from '@/api/media/musicCategory'
  import { formatDate } from '@/utils/format'
  import { downloadFile } from '@/utils/downloadFile'

  defineOptions({
    name: 'MinimaxVoiceClone'
  })

  // 表单数据
  const formData = reactive({
    text: '',
    referenceAudioId: null,
    referenceAudioPath: [],
    targetCategoryId: null,
    targetCategoryPath: [],
    model: 'speech-01',
    speed: 1.0,
    volume: 1.0,
    pitch: 0,
    audioSampleRate: 32000,
    bitRate: 128000,
    format: 'mp3'
  })

  // 表单校验规则
  const rules = {
    text: [
      { required: true, message: '请输入要转换的文本', trigger: 'blur' },
      { min: 1, max: 5000, message: '文本长度应在1-5000字符之间', trigger: 'blur' }
    ],
    referenceAudioId: [{ required: true, message: '请选择参考音色', trigger: 'change' }],
    targetCategoryId: [{ required: true, message: '请选择存放位置', trigger: 'change' }]
  }

  // 组件状态
  const loading = ref(false)
  const advancedOpen = ref([])
  const cloneForm = ref()

  // 音频播放状态
  const playingAudios = ref(new Map()) // 存储正在播放的音频实例
  const playingStates = ref(new Map()) // 存储播放状态

  // 级联选择器配置
  const cascaderProps = {
    value: 'ID',
    label: 'name',
    children: 'children',
    emitPath: false
  }

  // 音色选项和目标分类选项
  const voiceOptions = ref([])
  const targetCategoryOptions = ref([])
  const selectedVoice = ref(null)

  // 任务列表
  const tasks = ref([])
  const pagination = reactive({
    page: 1,
    pageSize: 10,
    total: 0
  })

  // 音乐数据映射
  const musicMap = ref(new Map())

  // 获取音乐分类列表
  const fetchCategories = async () => {
    try {
      const res = await getCategoryList()
      if (res.code === 0 && res.data) {
        return res.data
      }
      return []
    } catch (error) {
      console.error('获取分类失败:', error)
      return []
    }
  }

  // 获取指定分类下的音乐列表
  const fetchMusicByCategory = async (categoryId) => {
    try {
      const res = await getMusicList({
        page: 1,
        pageSize: 1000, // 获取所有音乐
        categoryId: categoryId
      })
      if (res.code === 0 && res.data) {
        return res.data.list || []
      }
      return []
    } catch (error) {
      console.error('获取音乐失败:', error)
      return []
    }
  }

  // 检查分类是否为ID42或其子分类
  const isVoiceCategoryOrChild = (categoryId, categories) => {
    if (categoryId === 42) return true

    const findInCategories = (cats) => {
      for (const cat of cats) {
        if (cat.ID === 42) {
          // 检查是否为42的子分类
          const checkChildren = (children) => {
            for (const child of children || []) {
              if (child.ID === categoryId) return true
              if (child.children && checkChildren(child.children)) return true
            }
            return false
          }
          return checkChildren(cat.children || [])
        }
        if (cat.children && findInCategories(cat.children)) return true
      }
      return false
    }

    return findInCategories(categories)
  }

  // 构建音色选项（仅ID42及其子分类）
  const buildVoiceOptions = async (categories) => {
    const options = []

    const processCategory = async (cat, parentPath = []) => {
      const currentPath = [...parentPath, cat]
      const isVoiceCategory = isVoiceCategoryOrChild(cat.ID, categories)

      if (isVoiceCategory && cat.ID !== 0) {
        // 获取该分类下的音乐
        const musicList = await fetchMusicByCategory(cat.ID)

        if (musicList.length > 0) {
          const categoryOption = {
            ID: cat.ID,
            name: cat.name,
            children: musicList.map((music) => ({
              ID: music.ID,
              name: music.name,
              categoryId: music.categoryId,
              categoryName: cat.name,
              fileUrl:
                music.fileUrl?.indexOf('http') === 0
                  ? music.fileUrl
                  : import.meta.env.VITE_BASE_API + '/' + music.fileUrl,
              isMusic: true
            }))
          }

          // 将音乐添加到映射中
          musicList.forEach((music) => {
            musicMap.value.set(music.ID, {
              ...music,
              categoryName: cat.name,
              fileUrl:
                music.fileUrl?.indexOf('http') === 0
                  ? music.fileUrl
                  : import.meta.env.VITE_BASE_API + '/' + music.fileUrl
            })
          })

          options.push(categoryOption)
        }
      }

      // 递归处理子分类
      if (cat.children) {
        for (const child of cat.children) {
          await processCategory(child, currentPath)
        }
      }
    }

    for (const category of categories) {
      if (category.ID !== 0) {
        // 排除"全部分类"
        await processCategory(category)
      }
    }

    return options
  }

  // 构建目标分类选项（排除ID42及其子分类）
  const buildTargetCategoryOptions = (categories) => {
    const options = []

    const processCategory = (cat) => {
      if (cat.ID === 0) return null // 排除"全部分类"

      const isVoiceCategory = isVoiceCategoryOrChild(cat.ID, categories)
      if (isVoiceCategory) return null // 排除音色分类

      const option = {
        ID: cat.ID,
        name: cat.name,
        children: []
      }

      if (cat.children) {
        for (const child of cat.children) {
          const childOption = processCategory(child)
          if (childOption) {
            option.children.push(childOption)
          }
        }
      }

      return option
    }

    for (const category of categories) {
      const option = processCategory(category)
      if (option) {
        options.push(option)
      }
    }

    return options
  }

  // 初始化数据
  const initData = async () => {
    const categories = await fetchCategories()
    voiceOptions.value = await buildVoiceOptions(categories)
    targetCategoryOptions.value = buildTargetCategoryOptions(categories)
    await fetchTasks()
  }
