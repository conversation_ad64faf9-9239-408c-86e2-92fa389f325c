<template>
  <div class="runninghub-voice-clone">
    <el-card class="clone-form-card">
      <el-form ref="cloneForm" :model="formData" :rules="rules" label-width="120px" size="large">
        <!-- 文本输入 -->
        <el-form-item label="转换文本" prop="text">
          <el-input
            v-model="formData.text"
            type="textarea"
            :rows="4"
            placeholder="请输入要转换成语音的文本内容..."
            maxlength="1000"
            show-word-limit
            class="text-input"
          />
        </el-form-item>

        <!-- 音色选择 -->
        <el-form-item label="选择音色" prop="referenceAudioId">
          <div class="voice-selection">
            <el-cascader
              v-model="formData.referenceAudioPath"
              :options="voiceOptions"
              :props="cascaderProps"
              placeholder="请选择参考音色"
              style="width: 100%"
              filterable
              @change="handleVoiceSelect"
            />
            <div v-if="selectedVoice" class="selected-voice-preview">
              <div class="voice-info">
                <span class="voice-name">{{ selectedVoice.name }}</span>
                <span class="voice-category">{{ selectedVoice.categoryName }}</span>
              </div>
              <div class="voice-preview-controls">
                <el-button
                  :type="playingStates.get(selectedVoice.fileUrl) ? 'warning' : 'success'"
                  size="small"
                  @click="toggleAudioPlay(selectedVoice.fileUrl)"
                >
                  <el-icon v-if="playingStates.get(selectedVoice.fileUrl)"><VideoPause /></el-icon>
                  <el-icon v-else><VideoPlay /></el-icon>
                  {{ playingStates.get(selectedVoice.fileUrl) ? '暂停' : '播放' }}
                </el-button>
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 存放位置选择 -->
        <el-form-item label="存放位置" prop="targetCategoryId">
          <el-cascader
            v-model="formData.targetCategoryPath"
            :options="targetCategoryOptions"
            :props="cascaderProps"
            placeholder="请选择生成音频的存放分类"
            style="width: 100%"
            filterable
            @change="handleTargetCategorySelect"
          />
        </el-form-item>

        <!-- 高级设置 -->
        <el-form-item label="" prop="">
          <div class="advanced-settings-wrapper">
            <el-collapse v-model="advancedOpen">
              <el-collapse-item title="高级设置" name="advanced">
                <div class="advanced-settings-content">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="语言" label-width="120px">
                        <el-select v-model="formData.language" placeholder="选择语言" style="width: 100%">
                          <el-option label="中文" value="zh" />
                          <el-option label="英文" value="en" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="模型" label-width="120px">
                        <el-select v-model="formData.model" placeholder="选择模型" style="width: 100%">
                          <el-option label="Index-TTS" value="Index-TTS" />
                          <el-option label="IndexTTS-1.5" value="IndexTTS-1.5" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label-width="120px">
                        <template #label>
                          <span>温度</span>
                          <el-tooltip
                            content="控制输出的随机性。值越低，输出越稳定；值越高，输出越随机创新"
                            placement="top"
                          >
                            <el-icon style="margin-left: 4px; color: var(--el-color-info)"><QuestionFilled /></el-icon>
                          </el-tooltip>
                        </template>
                        <div class="slider-container">
                          <el-slider
                            v-model="formData.temperature"
                            :min="0.1"
                            :max="1.5"
                            :step="0.1"
                            show-tooltip
                            class="custom-slider"
                          />
                          <span class="slider-value">{{ formData.temperature }}</span>
                        </div>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label-width="120px">
                        <template #label>
                          <span>TopP</span>
                          <el-tooltip
                            content="控制生成的多样性。值越小，输出越确定；值越大，输出越随机多样"
                            placement="top"
                          >
                            <el-icon style="margin-left: 4px; color: var(--el-color-info)"><QuestionFilled /></el-icon>
                          </el-tooltip>
                        </template>
                        <div class="slider-container">
                          <el-slider
                            v-model="formData.topP"
                            :min="0"
                            :max="1.0"
                            :step="0.1"
                            show-tooltip
                            class="custom-slider"
                          />
                          <span class="slider-value">{{ formData.topP }}</span>
                        </div>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label-width="120px">
                        <template #label>
                          <span>TopK</span>
                          <el-tooltip
                            content="从前K个最可能的候选中选择。值越小，输出越保守；值越大，候选范围越广"
                            placement="top"
                          >
                            <el-icon style="margin-left: 4px; color: var(--el-color-info)"><QuestionFilled /></el-icon>
                          </el-tooltip>
                        </template>
                        <el-input-number v-model="formData.topK" :min="1" :max="100" style="width: 100%" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label-width="120px">
                        <template #label>
                          <span>重复惩罚</span>
                          <el-tooltip content="防止生成重复内容的程度。值越高，重复性越低，输出越多样" placement="top">
                            <el-icon style="margin-left: 4px; color: var(--el-color-info)"><QuestionFilled /></el-icon>
                          </el-tooltip>
                        </template>
                        <div class="slider-container">
                          <el-slider
                            v-model="formData.repetitionPenalty"
                            :min="1"
                            :max="20"
                            :step="1"
                            show-tooltip
                            class="custom-slider"
                          />
                          <span class="slider-value">{{ formData.repetitionPenalty }}</span>
                        </div>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="submitClone" size="large">
            <el-icon><MagicStick /></el-icon>
            开始克隆
          </el-button>
          <el-button @click="resetForm" size="large">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 任务列表 -->
    <el-card class="task-list-card" v-if="tasks.length > 0">
      <template #header>
        <div class="card-header">
          <span>克隆任务</span>
          <el-button text @click="refreshTasks">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>

      <el-table :data="tasks" style="width: 100%">
        <el-table-column prop="text" label="文本内容" min-width="200">
          <template #default="scope">
            <div class="text-preview">{{ scope.row.text.substring(0, 50) }}...</div>
          </template>
        </el-table-column>
        <el-table-column prop="referenceAudioName" label="声源" width="150">
          <template #default="scope">
            <span v-if="scope.row.referenceAudioName">{{ scope.row.referenceAudioName }}</span>
            <span v-else class="text-gray-400">--</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="时长" width="100">
          <template #default="scope">
            <span v-if="scope.row.duration">{{ formatDuration(scope.row.duration) }}</span>
            <span v-else class="text-gray-400">--</span>
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.startTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button
              v-if="scope.row.audioUrl"
              :type="playingStates.get(scope.row.audioUrl) ? 'warning' : 'success'"
              size="small"
              @click="toggleAudioPlay(scope.row.audioUrl)"
            >
              <el-icon v-if="playingStates.get(scope.row.audioUrl)"><VideoPause /></el-icon>
              <el-icon v-else><VideoPlay /></el-icon>
              {{ playingStates.get(scope.row.audioUrl) ? '暂停' : '播放' }}
            </el-button>
            <el-button v-if="scope.row.audioUrl" type="primary" size="small" @click="downloadAudio(scope.row)">
              <el-icon><Download /></el-icon>
              下载
            </el-button>
            <el-button
              v-if="scope.row.status === 'RUNNING' || scope.row.status === 'PENDING'"
              type="info"
              size="small"
              @click="checkTaskStatus(scope.row.taskId)"
            >
              <el-icon><Refresh /></el-icon>
              刷新状态
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage } from 'element-plus'
  import { MagicStick, Refresh, VideoPlay, VideoPause, Download, QuestionFilled } from '@element-plus/icons-vue'
  import { createVoiceCloneTask, getVoiceCloneTaskList, getVoiceCloneTaskStatus } from '@/api/ai/voiceClone'
  import { getMusicList } from '@/api/media/music'
  import { getCategoryList } from '@/api/media/musicCategory'
  import { formatDate } from '@/utils/format'
  import { downloadFile } from '@/utils/downloadFile'

  defineOptions({
    name: 'RunningHubVoiceClone'
  })

  // 表单数据
  const formData = reactive({
    text: '',
    referenceAudioId: null,
    referenceAudioPath: [],
    targetCategoryId: null,
    targetCategoryPath: [],
    language: 'zh',
    model: 'Index-TTS',
    speed: 1.0,
    temperature: 1.5,
    topP: 0,
    topK: 3,
    repetitionPenalty: 15,
    lengthPenalty: 0,
    numBeams: 3,
    maxMelTokens: 600,
    sentenceSplit: 'auto'
  })

  // 表单校验规则
  const rules = {
    text: [
      { required: true, message: '请输入要转换的文本', trigger: 'blur' },
      { min: 1, max: 1000, message: '文本长度应在1-1000字符之间', trigger: 'blur' }
    ],
    referenceAudioId: [{ required: true, message: '请选择参考音色', trigger: 'change' }],
    targetCategoryId: [{ required: true, message: '请选择存放位置', trigger: 'change' }]
  }

  // 组件状态
  const loading = ref(false)
  const advancedOpen = ref([])
  const cloneForm = ref()

  // 音频播放状态
  const playingAudios = ref(new Map()) // 存储正在播放的音频实例
  const playingStates = ref(new Map()) // 存储播放状态

  // 级联选择器配置
  const cascaderProps = {
    value: 'ID',
    label: 'name',
    children: 'children',
    emitPath: false
  }

  // 音色选项和目标分类选项
  const voiceOptions = ref([])
  const targetCategoryOptions = ref([])
  const selectedVoice = ref(null)

  // 任务列表
  const tasks = ref([])
  const pagination = reactive({
    page: 1,
    pageSize: 10,
    total: 0
  })

  // 音乐数据映射
  const musicMap = ref(new Map())

  // 方法占位符 - 这里应该包含原有的所有方法
  // 为了简化，暂时只添加基本方法
  const handleVoiceSelect = (value) => {
    console.log('Voice selected:', value)
  }

  const handleTargetCategorySelect = (value) => {
    console.log('Target category selected:', value)
  }

  const submitClone = () => {
    console.log('Submit clone')
  }

  const resetForm = () => {
    console.log('Reset form')
  }

  const refreshTasks = () => {
    console.log('Refresh tasks')
  }

  const toggleAudioPlay = (url) => {
    console.log('Toggle audio play:', url)
  }

  const downloadAudio = (task) => {
    console.log('Download audio:', task)
  }

  const checkTaskStatus = (taskId) => {
    console.log('Check task status:', taskId)
  }

  const getStatusType = (status) => {
    const statusMap = {
      RUNNING: 'warning',
      SUCCEEDED: 'success',
      FAILED: 'danger',
      PENDING: 'info'
    }
    return statusMap[status] || 'info'
  }

  const getStatusText = (status) => {
    const statusMap = {
      RUNNING: '处理中',
      SUCCEEDED: '已完成',
      FAILED: '失败',
      PENDING: '等待中'
    }
    return statusMap[status] || '未知'
  }

  const formatDuration = (seconds) => {
    if (!seconds || seconds <= 0) return '--'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const handleSizeChange = (val) => {
    pagination.pageSize = val
  }

  const handleCurrentChange = (val) => {
    pagination.page = val
  }

  onMounted(() => {
    console.log('RunningHub Voice Clone component mounted')
  })
</script>

<style scoped>
  .runninghub-voice-clone {
    width: 100%;
  }

  .clone-form-card {
    margin-bottom: 30px;
  }

  .text-input {
    width: 100%;
  }

  .voice-selection {
    width: 100%;
  }

  .selected-voice-preview {
    margin-top: 15px;
    padding: 15px;
    background: var(--el-fill-color-light);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .voice-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  .voice-name {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  .voice-category {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .voice-preview-controls {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .task-list-card {
    margin-top: 30px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .text-preview {
    line-height: 1.5;
    color: var(--el-text-color-primary);
  }

  .pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }

  .advanced-settings-wrapper {
    width: 100%;
  }

  .advanced-settings-content {
    margin: 0;
    padding: 0;
  }

  .slider-container {
    display: flex;
    align-items: center;
    gap: 15px;
    width: 100%;
  }

  .custom-slider {
    flex: 1;
    margin: 0;
  }

  .slider-value {
    min-width: 40px;
    text-align: center;
    font-size: 12px;
    color: var(--el-text-color-regular);
    background: var(--el-fill-color-light);
    padding: 2px 8px;
    border-radius: 4px;
  }

  :deep(.el-collapse-item__header) {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    padding: 0 20px;
    height: 48px;
    line-height: 48px;
  }

  :deep(.el-form-item) {
    margin-bottom: 18px;
  }

  :deep(.el-collapse-item__content) {
    padding-bottom: 0;
    padding-left: 0;
    padding-right: 0;
  }

  :deep(.el-select) {
    width: 100%;
  }

  :deep(.el-input-number) {
    width: 100%;
  }

  :deep(.el-collapse) {
    border: none;
  }

  :deep(.el-collapse-item) {
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
  }

  :deep(.el-collapse-item__header) {
    border-bottom: 1px solid var(--el-border-color-lighter);
    background: var(--el-fill-color-extra-light);
    padding: 0 16px;
    border-radius: 6px 6px 0 0;
  }

  :deep(.el-collapse-item__content) {
    padding: 16px;
    border-radius: 0 0 6px 6px;
  }

  :deep(.el-slider__runway) {
    height: 6px;
  }

  :deep(.el-slider__button) {
    width: 16px;
    height: 16px;
  }
</style>
