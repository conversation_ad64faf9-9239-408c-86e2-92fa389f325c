import service from '@/utils/request'

// createMinimaxVoiceCloneTask 创建 MiniMax 人声克隆任务
export const createMinimaxVoiceCloneTask = (data) => {
  return service({
    url: '/ai/minimax-voice-clone/create',
    method: 'post',
    data
  })
}

// getMinimaxVoiceCloneTaskList 获取 MiniMax 人声克隆任务列表
export const getMinimaxVoiceCloneTaskList = (params) => {
  return service({
    url: '/ai/minimax-voice-clone/list',
    method: 'get',
    params
  })
}

// getMinimaxVoiceCloneTaskStatus 获取 MiniMax 人声克隆任务状态
export const getMinimaxVoiceCloneTaskStatus = (taskId) => {
  return service({
    url: `/ai/minimax-voice-clone/status/${taskId}`,
    method: 'get'
  })
}
