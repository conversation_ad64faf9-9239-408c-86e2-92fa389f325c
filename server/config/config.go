package config

type Server struct {
	JWT       JWT     `mapstructure:"jwt" json:"jwt" yaml:"jwt"`
	Zap       Zap     `mapstructure:"zap" json:"zap" yaml:"zap"`
	Redis     Redis   `mapstructure:"redis" json:"redis" yaml:"redis"`
	RedisList []Redis `mapstructure:"redis-list" json:"redis-list" yaml:"redis-list"`
	Mongo     Mongo   `mapstructure:"mongo" json:"mongo" yaml:"mongo"`
	Email     Email   `mapstructure:"email" json:"email" yaml:"email"`
	System    System  `mapstructure:"system" json:"system" yaml:"system"`
	Captcha   Captcha `mapstructure:"captcha" json:"captcha" yaml:"captcha"`
	// auto
	AutoCode Autocode `mapstructure:"autocode" json:"autocode" yaml:"autocode"`
	// gorm
	Mysql  Mysql           `mapstructure:"mysql" json:"mysql" yaml:"mysql"`
	Mssql  Mssql           `mapstructure:"mssql" json:"mssql" yaml:"mssql"`
	Pgsql  Pgsql           `mapstructure:"pgsql" json:"pgsql" yaml:"pgsql"`
	Oracle Oracle          `mapstructure:"oracle" json:"oracle" yaml:"oracle"`
	Sqlite Sqlite          `mapstructure:"sqlite" json:"sqlite" yaml:"sqlite"`
	DBList []SpecializedDB `mapstructure:"db-list" json:"db-list" yaml:"db-list"`
	// oss
	Local        Local        `mapstructure:"local" json:"local" yaml:"local"`
	Qiniu        Qiniu        `mapstructure:"qiniu" json:"qiniu" yaml:"qiniu"`
	AliyunOSS    AliyunOSS    `mapstructure:"aliyun-oss" json:"aliyun-oss" yaml:"aliyun-oss"`
	HuaWeiObs    HuaWeiObs    `mapstructure:"hua-wei-obs" json:"hua-wei-obs" yaml:"hua-wei-obs"`
	TencentCOS   TencentCOS   `mapstructure:"tencent-cos" json:"tencent-cos" yaml:"tencent-cos"`
	AwsS3        AwsS3        `mapstructure:"aws-s3" json:"aws-s3" yaml:"aws-s3"`
	CloudflareR2 CloudflareR2 `mapstructure:"cloudflare-r2" json:"cloudflare-r2" yaml:"cloudflare-r2"`
	Minio        Minio        `mapstructure:"minio" json:"minio" yaml:"minio"`

	Excel Excel `mapstructure:"excel" json:"excel" yaml:"excel"`

	DiskList []DiskList `mapstructure:"disk-list" json:"disk-list" yaml:"disk-list"`

	// 跨域配置
	Cors CORS `mapstructure:"cors" json:"cors" yaml:"cors"`

	Douyin Douyin `mapstructure:"douyin" json:"douyin" yaml:"douyin"`

	DouyinMoreAPI DouyinMoreApi `mapstructure:"douyin-more-api" json:"douyin-more-api" yaml:"douyin-more-api"`

	// 快代理配置
	Kuaidaili Kuaidaili `mapstructure:"kuaidaili" json:"kuaidaili" yaml:"kuaidaili"`

	// 淘辣了API配置
	Taolale Taolale `mapstructure:"taolale" json:"taolale" yaml:"taolale"`

	// 阿里云通义万相配置
	TongYi TongYi `mapstructure:"tongyi" json:"tongyi" yaml:"tongyi"`

	// 阿里云IMS配置
	IMS IMS `mapstructure:"ims" json:"ims" yaml:"ims"`
	// RunningHub
	RunningHub RunningHub `mapstructure:"runninghub" json:"runninghub" yaml:"runninghub"`

	// AI
	ArkAI ArkAI `mapstructure:"ark-ai" json:"ark-ai" yaml:"ark-ai"`
	Ai    Ai    `mapstructure:"ai" json:"ai" yaml:"ai"`
}

// RunningHub RunningHub配置
type RunningHub struct {
	ApiKey               string `mapstructure:"api-key" json:"api-key" yaml:"api-key"`
	VoiceCloneWorkflowId string `mapstructure:"voice-clone-workflow-id" json:"voice-clone-workflow-id" yaml:"voice-clone-workflow-id"`
}

type Kuaidaili struct {
	ApiURL      string           `mapstructure:"api-url" json:"api-url" yaml:"api-url"`
	SecretID    string           `mapstructure:"secret-id" json:"secret-id" yaml:"secret-id"`
	SecretKey   string           `mapstructure:"secret-key" json:"secret-key" yaml:"secret-key"`
	Name        string           `mapstructure:"name" json:"name" yaml:"name"`
	Username    string           `mapstructure:"username" json:"username" yaml:"username"`
	Password    string           `mapstructure:"password" json:"password" yaml:"password"`
	Port        string           `mapstructure:"port" json:"port" yaml:"port"`
	TunnelURL   string           `mapstructure:"tunnel-url" json:"tunnel-url" yaml:"tunnel-url"`
	Credentials []KuaidailiCreds `mapstructure:"credentials" json:"credentials" yaml:"credentials"`
}

// KuaidailiCreds 快代理凭证配置
type KuaidailiCreds struct {
	Name      string `mapstructure:"name" json:"name" yaml:"name"`
	SecretID  string `mapstructure:"secret-id" json:"secret-id" yaml:"secret-id"`
	SecretKey string `mapstructure:"secret-key" json:"secret-key" yaml:"secret-key"`
}

// Taolale 淘辣了API配置
type Taolale struct {
	ApiURL string `mapstructure:"api-url" json:"api-url" yaml:"api-url"`
	ApiKey string `mapstructure:"api-key" json:"api-key" yaml:"api-key"`
}

// 通义万相配置
type TongYi struct {
	ApiKey string `mapstructure:"api-key" json:"api-key" yaml:"api-key"`
}

type ArkAI struct {
	APIKey string `mapstructure:"api-key" json:"api-key" yaml:"api-key"`
	APIURL string `mapstructure:"api-url" json:"api-url" yaml:"api-url"`
}
