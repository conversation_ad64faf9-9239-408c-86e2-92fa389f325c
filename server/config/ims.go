package config

type IMS struct {
	AccessKeyId             string `mapstructure:"access-key-id" json:"access-key-id" yaml:"access-key-id"`
	AccessKeySecret         string `mapstructure:"access-key-secret" json:"access-key-secret" yaml:"access-key-secret"`
	Endpoint                string `mapstructure:"endpoint" json:"endpoint" yaml:"endpoint"`
	RegionId                string `mapstructure:"region-id" json:"region-id" yaml:"region-id"`
	BucketName              string `mapstructure:"bucket-name" json:"bucket-name" yaml:"bucket-name"`
	BucketUrl               string `mapstructure:"bucket-url" json:"bucket-url" yaml:"bucket-url"`
	TextWatermarkTemplateId string `mapstructure:"text-watermark-template-id" json:"text-watermark-template-id" yaml:"text-watermark-template-id"`
	TranscodeTemplateId     string `mapstructure:"transcode-template-id" json:"transcode-template-id" yaml:"transcode-template-id"`
}
