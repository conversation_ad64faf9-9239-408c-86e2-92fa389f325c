package ai

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
)

type RouterGroup struct {
	VideoMultiLensRouter
	VideoRouter
	AutoVideoRouter
	VoiceCloneRouter
	MinimaxVoiceCloneRouter
}

var (
	videoMultiLensApi    = v1.ApiGroupApp.AiApiGroup.VideoMultiLensApi
	videoApi             = v1.ApiGroupApp.AiApiGroup.VideoApi
	autoVideoApi         = v1.ApiGroupApp.AiApiGroup.AutoVideoApi
	voiceCloneApi        = v1.ApiGroupApp.AiApiGroup.VoiceCloneApi
	minimaxVoiceCloneApi = v1.ApiGroupApp.AiApiGroup.MinimaxVoiceCloneApi
)
