package initialize

import (
	"os"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"github.com/flipped-aurora/gin-vue-admin/server/model/example"
	"github.com/flipped-aurora/gin-vue-admin/server/model/media"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

func Gorm() *gorm.DB {
	switch global.GVA_CONFIG.System.DbType {
	case "mysql":
		global.GVA_ACTIVE_DBNAME = &global.GVA_CONFIG.Mysql.Dbname
		return GormMysql()
	case "pgsql":
		global.GVA_ACTIVE_DBNAME = &global.GVA_CONFIG.Pgsql.Dbname
		return GormPgSql()
	case "oracle":
		global.GVA_ACTIVE_DBNAME = &global.GVA_CONFIG.Oracle.Dbname
		return GormOracle()
	case "mssql":
		global.GVA_ACTIVE_DBNAME = &global.GVA_CONFIG.Mssql.Dbname
		return GormMssql()
	case "sqlite":
		global.GVA_ACTIVE_DBNAME = &global.GVA_CONFIG.Sqlite.Dbname
		return GormSqlite()
	default:
		global.GVA_ACTIVE_DBNAME = &global.GVA_CONFIG.Mysql.Dbname
		return GormMysql()
	}
}

func RegisterTables() {
	db := global.GVA_DB
	err := db.AutoMigrate(

		system.SysApi{},
		system.SysIgnoreApi{},
		system.SysUser{},
		system.SysBaseMenu{},
		system.JwtBlacklist{},
		system.SysAuthority{},
		system.SysDictionary{},
		system.SysOperationRecord{},
		system.SysAutoCodeHistory{},
		system.SysDictionaryDetail{},
		system.SysBaseMenuParameter{},
		system.SysBaseMenuBtn{},
		system.SysAuthorityBtn{},
		system.SysAutoCodePackage{},
		system.SysExportTemplate{},
		system.Condition{},
		system.JoinTemplate{},
		system.SysParams{},

		example.ExaFile{},
		example.ExaCustomer{},
		example.ExaFileChunk{},
		example.ExaFileUploadAndDownload{},
		example.ExaAttachmentCategory{},
		douyin.DyAweme{},
		douyin.IpPool{},
		douyin.DyUser{},
		douyin.DyProductSelection{},
		douyin.FlameUser{},
		douyin.DyAuthUser{},
		douyin.PhoneBalance{},
		douyin.DyTopComment{},
		douyin.DyChatContact{},
		douyin.DyChatContactUser{},
		douyin.DyChatMessage{},
		ai.VideoTask{},
		ai.VideoMultiLensTask{},
		ai.VideoLens{},
		media.Music{},
		media.MusicCategory{},
		creative.VideoCategory{},
		creative.AiModel{},
		creative.WatchTargetUser{},
		creative.WatchTargetPost{},
		media.DyMusic{},
		creative.Video{},
		creative.AutoPublishVideo{},
		creative.AutoPublishVideoTemplate{},
		creative.AutoPublishVideoRecord{},
		media.Image{},
		media.ImageCategory{},
		media.Video{},
		media.VideoCategory{},
		ai.AutoVideoTask{},
		media.Copywriting{},
		media.CopywritingCategory{},
		media.Topic{},
		media.TopicCategory{},
		ai.AutoVideoFavoriteActor{},
		ai.PromptTemplate{},
		ai.PromptCategory{},
		ai.StickerTemplate{},
		ai.VoiceCloneTask{},
		douyin.DyProductManual{},
		ai.MinimaxVoiceCloneTask{},
	)
	if err != nil {
		global.GVA_LOG.Error("register table failed", zap.Error(err))
		os.Exit(0)
	}

	err = bizModel()

	if err != nil {
		global.GVA_LOG.Error("register biz_table failed", zap.Error(err))
		os.Exit(0)
	}
	global.GVA_LOG.Info("register table success")
}
