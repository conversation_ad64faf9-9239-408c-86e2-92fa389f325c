package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/logic"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"go.uber.org/zap"
)

// 处理抖音作品
type DsiposeDyAwemeTask struct {
}

func (s DsiposeDyAwemeTask) Exec(arg any) (err error) {
	taskTitle := "处理抖音作品任务"

	dyAwemeLogic := logic.DyAwemeLogic{}
	// 从dy_aweme表中获取sys_dispose为 9, 10, 11, 12的数据
	var dyAwemes []douyin.DyAweme
	_ = global.GVA_DB.Model(&douyin.DyAweme{}).Where("sys_dispose in (?)", []int{9, 10, 11, 12}).
		Limit(100).Find(&dyAwemes).Error

	fmt.Printf("获取到%d条待处理数据\n", len(dyAwemes))

	// 根据unique分组
	uniqueMap := make(map[string][]douyin.DyAweme)
	for _, item := range dyAwemes {
		uniqueMap[item.UniqueId] = append(uniqueMap[item.UniqueId], item)
	}

	dyAwemeService := &service.ServiceGroupApp.DouyinServiceGroup.DyAwemeService

	// 处理uniqueMap，每个uniqueId对应一个协程
	var wg sync.WaitGroup
	for uniqueId, items := range uniqueMap {
		wg.Add(1)
		go func(uniqueId string, items []douyin.DyAweme) {
			defer wg.Done()
			ctx := context.Background()
			lockKey := fmt.Sprintf("dispose_dy_aweme_task:%s", uniqueId)
			// 锁存在时，跳过
			if global.GVA_REDIS.Exists(ctx, lockKey).Val() > 0 {
				delete(uniqueMap, uniqueId)
				global.GVA_LOG.Error(fmt.Sprintf("%s: 用户上一个任务仍未结束:unique_id:%s", taskTitle, uniqueId), zap.Error(err))
				return
			}
			_, err := global.GVA_REDIS.SetNX(ctx, lockKey, 1, time.Duration(20*len(items))*time.Second).Result()

			// 根据uniqueId获取dy_user信息
			var dyUser *douyin.DyUser
			err = global.GVA_DB.Model(&douyin.DyUser{}).Where("unique_id = ?", uniqueId).First(&dyUser).Error
			if err != nil {
				global.GVA_LOG.Error(fmt.Sprintf("%s: 获取用户资料失败:unique_id:%s", taskTitle, uniqueId), zap.Error(err))
				return
			}

			for _, aweme := range items {
				fmt.Printf("开始处理:作品id:%d, 用户:%s, sys_dispose:%d \n", aweme.ID, aweme.Nickname, aweme.SysDispose)
				// 改为隐藏
				if aweme.SysDispose == 10 {
					err = dyAwemeLogic.EditAwemePermission(dyUser, &aweme, 1, aweme.SysDisposeMethod)
					if err != nil {
						global.GVA_LOG.Error(fmt.Sprintf("%s: 更改作品权限失败:id:%d", taskTitle, aweme.ID), zap.Error(err))
						continue
					}
				} else if aweme.SysDispose == 12 {
					err = dyAwemeLogic.DeleteAweme(dyUser, &aweme, aweme.SysDisposeMethod)
					if err != nil {
						global.GVA_LOG.Error(fmt.Sprintf("%s: 删除作品失败:id:%d", taskTitle, aweme.ID), zap.Error(err))
						continue
					}
				}

				err = dyAwemeService.UpdateAweme(&aweme)
				if err != nil {
					global.GVA_LOG.Error(fmt.Sprintf("%s: 更新数据库失败:id:%d", taskTitle, aweme.ID), zap.Error(err))
					continue
				}
				fmt.Printf("处理完成:作品id:%d, 用户:%s, sys_dispose:%d \n", aweme.ID, aweme.Nickname, aweme.SysDispose)

				// 暂停10秒
				time.Sleep(10 * time.Second)
			}

			// 删除锁
			_ = global.GVA_REDIS.Del(ctx, lockKey).Err()
		}(uniqueId, items)

		wg.Wait()
	}

	global.GVA_LOG.Info(fmt.Sprintf("%s: 执行完成,共处理数据%d条", taskTitle, len(dyAwemes)))
	return
}
