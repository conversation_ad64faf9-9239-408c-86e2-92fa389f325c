package service

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/response"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"go.uber.org/zap"
)

// 异步检查上传视频结果任务
type AsyncUploadVideoResultTask struct {
}

func (s AsyncUploadVideoResultTask) Exec(arg any) (err error) {
	taskTitle := "检查上传视频结果任务"
	var records []creative.AutoPublishVideoRecord
	err = global.GVA_DB.Where("status = ?", 1).Find(&records).Error
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("%s: 获取发布列表失败", taskTitle), zap.Error(err))
		return
	}

	videoService := &service.ServiceGroupApp.CreativeServiceGroup.VideoService
	for _, record := range records {
		taskWarningFormat := fmt.Sprintf("%s: recordId:%d, uniqueId:%s", taskTitle, record.ID, record.UniqueId)

		// 比较当前时间和CreatedAt的时间差，如果超过30分钟，则跳过
		if record.CreatedAt.Add(31 * time.Minute).Before(time.Now()) {
			record.Status = 6
			record.Reason = fmt.Sprintf("%s, 查询视频上传结果GetVodUploadResult失败：%+v\n", taskWarningFormat, "超过30分钟没有返回结果")
			err = global.GVA_DB.Save(&record).Error
			if err != nil {
				fmt.Printf("%s, 保存查询结果失败：%+v\n", taskWarningFormat, record)
			}

			_ = videoService.UpdateVideoStatus(record.VideoId, 3)
			continue
		}
		// 通过id获取用户信息
		user, err := service.ServiceGroupApp.DouyinServiceGroup.DyUserService.GetUserByID(uint(record.DyUserId))
		if err != nil {
			record.Status = 6
			record.Reason = fmt.Sprintf("%s, 查询视频上传结果GetVodUploadResult失败：%+v\n", taskWarningFormat, err)
			err = global.GVA_DB.Save(&record).Error
			continue
		}

		vodeUploadResultReq := request.MoreCreatorApiGetVodUploadResultRequest{
			TaskId: record.TaskId,
			Proxy:  user.BindIP,
		}
		resp, err := service.ServiceGroupApp.DouyinServiceGroup.GetVodUploadResult(vodeUploadResultReq)
		if err != nil {
			record.Status = 6
			record.Reason = fmt.Sprintf("%s, 查询视频上传结果GetVodUploadResult失败：%+v\n", taskWarningFormat, err)
			err = global.GVA_DB.Save(&record).Error
			if err != nil {
				fmt.Printf("%s, 保存查询结果失败：%+v\n", taskWarningFormat, record)
			}

			_ = videoService.UpdateVideoStatus(record.VideoId, 3)
			continue
		}
		if resp.Data.Status != "completed" {
			record.Reason = fmt.Sprintf("%s, 视频尚未完成上传，status:%s\n", taskWarningFormat, resp.Data.Status)
			err = global.GVA_DB.Save(&record).Error
			if err != nil {
				fmt.Printf("%s, 保存查询结果失败：%+v\n", taskWarningFormat, record)
			}

			_ = videoService.UpdateVideoStatus(record.VideoId, 3)
			continue
		}
		var result response.MoreCreatorApiGetVodUploadResultDataResult
		// 解析resp.data.result是否为result的结构，如果是，则直接赋值给result
		if err = json.Unmarshal(resp.Data.Result, &result); err != nil {
			record.Status = 6
			errReuslt := string(resp.Data.Result)
			record.Reason = fmt.Sprintf("%s, 解析视频上传结果失败：%+v\n", taskWarningFormat, errReuslt)
			err = global.GVA_DB.Save(&record).Error
			err = global.GVA_DB.Save(&record).Error
			if err != nil {
				fmt.Printf("%s, 保存查询结果失败：%+v\n", taskWarningFormat, record)
			}

			_ = videoService.UpdateVideoStatus(record.VideoId, 3)
			continue
		}

		if len(result.Result.Results) > 0 {
			record.Vid = result.Result.Results[0].Vid
			record.Status = 2
		}

		err = global.GVA_DB.Save(&record).Error
		if err != nil {
			_ = videoService.UpdateVideoStatus(record.VideoId, 3)
			fmt.Printf("%s, 保存查询结果失败：%+v\n", taskWarningFormat, record)
			continue
		}
	}
	return nil
}
