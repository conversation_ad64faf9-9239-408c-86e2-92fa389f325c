CREATE TABLE
  IF NOT EXISTS `ai_minimax_voice_clone_tasks` (
    `id` bigint (20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `created_at` datetime DEFAULT NULL COMMENT '创建时间',
    `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
    `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
    `task_id` varchar(255) NOT NULL COMMENT '任务ID',
    `user_id` bigint (20) unsigned NOT NULL COMMENT '用户ID',
    `text` text NOT NULL COMMENT '要转换的文本',
    `reference_audio_name` varchar(255) DEFAULT NULL COMMENT '参考音频名称(声源)',
    `minimax_voice_id` varchar(255) DEFAULT NULL COMMENT 'MiniMax音色ID',
    `model` varchar(100) DEFAULT 'speech-01' COMMENT '模型',
    `speed` decimal(3, 2) DEFAULT 1.00 COMMENT '语速',
    `volume` decimal(3, 2) DEFAULT 1.00 COMMENT '音量',
    `pitch` decimal(3, 2) DEFAULT 0.00 COMMENT '音调',
    `audio_sample_rate` int DEFAULT 32000 COMMENT '音频采样率',
    `bit_rate` int DEFAULT 128000 COMMENT '比特率',
    `format` varchar(20) DEFAULT 'mp3' COMMENT '音频格式',
    `target_category_id` bigint (20) unsigned DEFAULT NULL COMMENT '目标分类ID',
    `status` varchar(50) NOT NULL DEFAULT 'PENDING' COMMENT '任务状态',
    `audio_url` varchar(1000) DEFAULT NULL COMMENT '生成的音频URL',
    `error_msg` text DEFAULT NULL COMMENT '错误信息',
    `start_time` datetime NOT NULL COMMENT '开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '结束时间',
    `duration` bigint (20) DEFAULT 0 COMMENT '音频时长(秒)',
    `media_id` varchar(255) DEFAULT NULL COMMENT '媒资ID',
    PRIMARY KEY (`id`),
    KEY `idx_task_id` (`task_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_minimax_voice_id` (`minimax_voice_id`),
    KEY `idx_status` (`status`),
    KEY `idx_deleted_at` (`deleted_at`)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = 'MiniMax人声克隆任务表';