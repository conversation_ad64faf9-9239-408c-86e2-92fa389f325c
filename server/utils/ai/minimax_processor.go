package ai

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"go.uber.org/zap"
)

// MinimaxProcessor MiniMax API 处理器
type MinimaxProcessor struct {
	apiKey  string
	groupId string
	baseURL string
}

// NewMinimaxProcessor 创建 MiniMax 处理器
func NewMinimaxProcessor() *MinimaxProcessor {
	return &MinimaxProcessor{
		apiKey:  global.GVA_CONFIG.Ai.MinimaxApiKey,
		groupId: global.GVA_CONFIG.Ai.MinimaxGroupId,
		baseURL: "https://api.minimax.chat",
	}
}

// MinimaxFileUploadResponse 文件上传响应
type MinimaxFileUploadResponse struct {
	File struct {
		FileId string `json:"file_id"`
	} `json:"file"`
	BaseResp struct {
		StatusCode int    `json:"status_code"`
		StatusMsg  string `json:"status_msg"`
	} `json:"base_resp"`
	InputSensitive     bool `json:"input_sensitive"`
	InputSensitiveType int  `json:"input_sensitive_type"`
}

// MinimaxVoiceCloneResponse 音频复刻响应
type MinimaxVoiceCloneResponse struct {
	BaseResp struct {
		StatusCode int    `json:"status_code"`
		StatusMsg  string `json:"status_msg"`
	} `json:"base_resp"`
	InputSensitive     bool `json:"input_sensitive"`
	InputSensitiveType int  `json:"input_sensitive_type"`
}

// MinimaxT2AResponse T2A 响应
type MinimaxT2AResponse struct {
	BaseResp struct {
		StatusCode int    `json:"status_code"`
		StatusMsg  string `json:"status_msg"`
	} `json:"base_resp"`
	Data struct {
		AudioFile string `json:"audio_file"`
		Duration  int64  `json:"duration"`
	} `json:"data"`
	InputSensitive     bool `json:"input_sensitive"`
	InputSensitiveType int  `json:"input_sensitive_type"`
}

// UploadAudioFile 上传音频文件
func (p *MinimaxProcessor) UploadAudioFile(audioData []byte, fileName string) (*MinimaxFileUploadResponse, error) {
	url := fmt.Sprintf("%s/v1/files/upload?GroupId=%s", p.baseURL, p.groupId)

	// 创建 multipart form
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加 purpose 字段
	err := writer.WriteField("purpose", "voice_clone")
	if err != nil {
		return nil, fmt.Errorf("写入 purpose 字段失败: %v", err)
	}

	// 添加文件字段
	part, err := writer.CreateFormFile("file", fileName)
	if err != nil {
		return nil, fmt.Errorf("创建文件字段失败: %v", err)
	}

	_, err = part.Write(audioData)
	if err != nil {
		return nil, fmt.Errorf("写入文件数据失败: %v", err)
	}

	err = writer.Close()
	if err != nil {
		return nil, fmt.Errorf("关闭 writer 失败: %v", err)
	}

	// 创建请求
	req, err := http.NewRequest("POST", url, &buf)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+p.apiKey)
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	global.GVA_LOG.Info("MiniMax 文件上传响应", zap.String("response", string(body)))

	var response MinimaxFileUploadResponse
	err = json.Unmarshal(body, &response)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	if response.BaseResp.StatusCode != 0 {
		return nil, fmt.Errorf("上传失败: %s", response.BaseResp.StatusMsg)
	}

	return &response, nil
}

// CloneVoice 克隆音色
func (p *MinimaxProcessor) CloneVoice(fileId, voiceId string) (*MinimaxVoiceCloneResponse, error) {
	url := fmt.Sprintf("%s/v1/voice_clone?GroupId=%s", p.baseURL, p.groupId)

	requestData := map[string]interface{}{
		"file_id":  fileId,
		"voice_id": voiceId,
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+p.apiKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	global.GVA_LOG.Info("MiniMax 音色克隆响应", zap.String("response", string(body)))

	var response MinimaxVoiceCloneResponse
	err = json.Unmarshal(body, &response)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	if response.BaseResp.StatusCode != 0 {
		return nil, fmt.Errorf("音色克隆失败: %s", response.BaseResp.StatusMsg)
	}

	return &response, nil
}

// TextToAudio 文本转语音
func (p *MinimaxProcessor) TextToAudio(text, voiceId, model string, speed, volume, pitch float64, audioSampleRate, bitRate int, format string) (*MinimaxT2AResponse, error) {
	url := fmt.Sprintf("%s/v1/t2a_v2?GroupId=%s", p.baseURL, p.groupId)

	requestData := map[string]interface{}{
		"model":              model,
		"text":               text,
		"voice_id":           voiceId,
		"speed":              speed,
		"volume":             volume,
		"pitch":              pitch,
		"audio_sample_rate":  audioSampleRate,
		"bitrate":            bitRate,
		"format":             format,
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+p.apiKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	global.GVA_LOG.Info("MiniMax T2A 响应", zap.String("response", string(body)))

	var response MinimaxT2AResponse
	err = json.Unmarshal(body, &response)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	if response.BaseResp.StatusCode != 0 {
		return nil, fmt.Errorf("文本转语音失败: %s", response.BaseResp.StatusMsg)
	}

	return &response, nil
}
