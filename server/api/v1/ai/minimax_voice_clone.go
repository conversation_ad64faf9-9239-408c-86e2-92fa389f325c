package ai

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type MinimaxVoiceCloneApi struct{}

// CreateMinimaxVoiceCloneTask 创建 MiniMax 人声克隆任务
// @Tags MinimaxVoiceClone
// @Summary 创建 MiniMax 人声克隆任务
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.MinimaxVoiceCloneTaskRequest true "任务参数"
// @Success 200 {object} response.Response{data=response.MinimaxVoiceCloneTaskResponse} "创建成功"
// @Router /ai/minimax-voice-clone/create [post]
func (api *MinimaxVoiceCloneApi) CreateMinimaxVoiceCloneTask(c *gin.Context) {
	var req request.MinimaxVoiceCloneTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取用户ID
	userId := utils.GetUserID(c)

	// 注入音乐服务依赖
	minimaxVoiceCloneService := service.ServiceGroupApp.AiServiceGroup.MinimaxVoiceCloneService
	minimaxVoiceCloneService.MusicService = &service.ServiceGroupApp.MediaServiceGroup.MusicService

	// 创建任务
	res, err := minimaxVoiceCloneService.CreateMinimaxVoiceCloneTask(req, userId)
	if err != nil {
		global.GVA_LOG.Error("创建 MiniMax 人声克隆任务失败!", zap.Error(err))
		response.FailWithMessage("创建任务失败: "+err.Error(), c)
		return
	}

	response.OkWithData(res, c)
}

// GetMinimaxVoiceCloneTaskStatus 获取 MiniMax 人声克隆任务状态
// @Tags MinimaxVoiceClone
// @Summary 获取 MiniMax 人声克隆任务状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param taskId path string true "任务ID"
// @Success 200 {object} response.Response{data=ai.MinimaxVoiceCloneTask} "获取成功"
// @Router /ai/minimax-voice-clone/status/{taskId} [get]
func (api *MinimaxVoiceCloneApi) GetMinimaxVoiceCloneTaskStatus(c *gin.Context) {
	taskId := c.Param("taskId")
	if taskId == "" {
		response.FailWithMessage("任务ID不能为空", c)
		return
	}

	task, err := service.ServiceGroupApp.AiServiceGroup.MinimaxVoiceCloneService.GetMinimaxVoiceCloneTaskStatus(taskId)
	if err != nil {
		global.GVA_LOG.Error("获取 MiniMax 人声克隆任务状态失败!", zap.Error(err))
		response.FailWithMessage("获取任务状态失败: "+err.Error(), c)
		return
	}

	response.OkWithData(task, c)
}

// GetMinimaxVoiceCloneTaskList 获取 MiniMax 人声克隆任务列表
// @Tags MinimaxVoiceClone
// @Summary 获取 MiniMax 人声克隆任务列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Success 200 {object} response.Response{data=response.PageResult} "获取成功"
// @Router /ai/minimax-voice-clone/list [get]
func (api *MinimaxVoiceCloneApi) GetMinimaxVoiceCloneTaskList(c *gin.Context) {
	var req request.MinimaxVoiceCloneTaskListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 获取用户ID
	userId := utils.GetUserID(c)

	tasks, total, err := service.ServiceGroupApp.AiServiceGroup.MinimaxVoiceCloneService.GetMinimaxVoiceCloneTaskList(req, userId)
	if err != nil {
		global.GVA_LOG.Error("获取 MiniMax 人声克隆任务列表失败!", zap.Error(err))
		response.FailWithMessage("获取任务列表失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     tasks,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}
