package ai

import (
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai"
	aiRequest "github.com/flipped-aurora/gin-vue-admin/server/model/ai/request"
	aiResponse "github.com/flipped-aurora/gin-vue-admin/server/model/ai/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/media"
	utilsAi "github.com/flipped-aurora/gin-vue-admin/server/utils/ai"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// MinimaxMusicServiceInterface 音乐服务接口，避免循环依赖
type MinimaxMusicServiceInterface interface {
	SaveMinimaxVoiceCloneToLibrary(taskId uint, audioUrl string, categoryId uint, userId uint) (musicUrl string, duration int64, mediaId string, err error)
}

type MinimaxVoiceCloneService struct {
	MusicService MinimaxMusicServiceInterface
}

// CreateMinimaxVoiceCloneTask 创建 MiniMax 人声克隆任务
func (s *MinimaxVoiceCloneService) CreateMinimaxVoiceCloneTask(
	req aiRequest.MinimaxVoiceCloneTaskRequest,
	userId uint,
) (aiResponse.MinimaxVoiceCloneTaskResponse, error) {
	// 验证必需参数
	if req.Text == "" {
		return aiResponse.MinimaxVoiceCloneTaskResponse{}, fmt.Errorf("文本不能为空")
	}
	if req.ReferenceAudioUrl == "" {
		return aiResponse.MinimaxVoiceCloneTaskResponse{}, fmt.Errorf("参考音频URL不能为空")
	}

	// 设置默认值
	if req.Model == "" {
		req.Model = "speech-01"
	}
	if req.Speed == 0 {
		req.Speed = 1.0
	}
	if req.Volume == 0 {
		req.Volume = 1.0
	}
	if req.Pitch == 0 {
		req.Pitch = 0.0
	}
	if req.AudioSampleRate == 0 {
		req.AudioSampleRate = 32000
	}
	if req.BitRate == 0 {
		req.BitRate = 128000
	}
	if req.Format == "" {
		req.Format = "mp3"
	}

	// 下载参考音频文件
	resp, err := http.Get(req.ReferenceAudioUrl)
	if err != nil {
		global.GVA_LOG.Error("下载参考音频文件失败", zap.String("url", req.ReferenceAudioUrl), zap.Error(err))
		return aiResponse.MinimaxVoiceCloneTaskResponse{}, fmt.Errorf("下载参考音频文件失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return aiResponse.MinimaxVoiceCloneTaskResponse{}, fmt.Errorf("下载参考音频文件失败，状态码: %d", resp.StatusCode)
	}

	audioData, err := io.ReadAll(resp.Body)
	if err != nil {
		global.GVA_LOG.Error("读取参考音频文件失败", zap.Error(err))
		return aiResponse.MinimaxVoiceCloneTaskResponse{}, fmt.Errorf("读取参考音频文件失败: %v", err)
	}

	// 生成文件名
	fileName := fmt.Sprintf("reference_audio_%d.mp3", time.Now().Unix())

	// 检查是否已经有对应的 minimax_voice_id
	var existingMusic media.Music
	err = global.GVA_DB.Where("file_url = ? AND minimax_voice_id != ''", req.ReferenceAudioUrl).First(&existingMusic).Error

	var voiceId string
	if err == nil && existingMusic.MinimaxVoiceId != "" {
		// 已存在 voice_id，直接使用
		voiceId = existingMusic.MinimaxVoiceId
		global.GVA_LOG.Info("使用已存在的 minimax_voice_id", zap.String("voiceId", voiceId))
	} else {
		// 需要创建新的 minimax_voice_id
		processor := utilsAi.NewMinimaxProcessor()

		// 1. 上传音频文件
		uploadResp, err := processor.UploadAudioFile(audioData, fileName)
		if err != nil {
			global.GVA_LOG.Error("上传音频文件到 MiniMax 失败", zap.Error(err))
			return aiResponse.MinimaxVoiceCloneTaskResponse{}, fmt.Errorf("上传音频文件失败: %v", err)
		}

		// 2. 生成唯一的 minimax_voice_id
		voiceId = fmt.Sprintf("voice_%d_%d", userId, time.Now().Unix())

		// 3. 克隆音色
		cloneResp, err := processor.CloneVoice(uploadResp.File.FileId, voiceId)
		if err != nil {
			global.GVA_LOG.Error("克隆音色失败", zap.Error(err))
			return aiResponse.MinimaxVoiceCloneTaskResponse{}, fmt.Errorf("克隆音色失败: %v", err)
		}

		global.GVA_LOG.Info("音色克隆成功",
			zap.String("voiceId", voiceId),
			zap.Any("response", cloneResp))

		// 4. 更新音乐库中的 minimax_voice_id
		if err == gorm.ErrRecordNotFound {
			// 如果音乐不存在，可能需要创建记录，但这里我们只更新现有记录
			global.GVA_LOG.Warn("未找到对应的音乐记录", zap.String("url", req.ReferenceAudioUrl))
		} else {
			// 更新现有记录的 minimax_voice_id
			err = global.GVA_DB.Model(&existingMusic).Update("minimax_voice_id", voiceId).Error
			if err != nil {
				global.GVA_LOG.Error("更新音乐记录的 minimax_voice_id 失败", zap.Error(err))
			}
		}
	}

	// 生成任务ID
	taskId := fmt.Sprintf("minimax_%d_%d", userId, time.Now().Unix())

	// 创建任务记录
	task := ai.MinimaxVoiceCloneTask{
		TaskId:             taskId,
		UserId:             userId,
		Text:               req.Text,
		ReferenceAudioName: req.ReferenceAudioName,
		VoiceId:            voiceId,
		Model:              req.Model,
		Speed:              req.Speed,
		Volume:             req.Volume,
		Pitch:              req.Pitch,
		AudioSampleRate:    req.AudioSampleRate,
		BitRate:            req.BitRate,
		Format:             req.Format,
		TargetCategoryId:   req.TargetCategoryId,
		Status:             "RUNNING",
		StartTime:          time.Now(),
	}

	err = global.GVA_DB.Create(&task).Error
	if err != nil {
		global.GVA_LOG.Error("创建 MiniMax 人声克隆任务记录失败", zap.Error(err))
		return aiResponse.MinimaxVoiceCloneTaskResponse{}, err
	}

	// 异步执行文本转语音
	go s.processTextToAudio(task.ID, voiceId, req)

	return aiResponse.MinimaxVoiceCloneTaskResponse{TaskId: taskId}, nil
}

// processTextToAudio 处理文本转语音
func (s *MinimaxVoiceCloneService) processTextToAudio(taskId uint, voiceId string, req aiRequest.MinimaxVoiceCloneTaskRequest) {
	processor := utilsAi.NewMinimaxProcessor()

	// 调用文本转语音 API
	t2aResp, err := processor.TextToAudio(
		req.Text, voiceId, req.Model,
		req.Speed, req.Volume, req.Pitch,
		req.AudioSampleRate, req.BitRate, req.Format,
	)

	updateData := map[string]interface{}{
		"end_time": time.Now(),
	}

	if err != nil {
		global.GVA_LOG.Error("文本转语音失败", zap.Uint("taskId", taskId), zap.Error(err))
		updateData["status"] = "FAILED"
		updateData["error_msg"] = err.Error()
	} else {
		global.GVA_LOG.Info("文本转语音成功",
			zap.Uint("taskId", taskId),
			zap.String("audioUrl", t2aResp.Data.AudioFile),
			zap.Int64("duration", t2aResp.Data.Duration))

		updateData["status"] = "SUCCEEDED"
		updateData["audio_url"] = t2aResp.Data.AudioFile
		updateData["duration"] = t2aResp.Data.Duration

		// 保存到音乐库
		if s.MusicService != nil && req.TargetCategoryId > 0 {
			var task ai.MinimaxVoiceCloneTask
			global.GVA_DB.First(&task, taskId)

			musicUrl, duration, mediaId, err := s.MusicService.SaveMinimaxVoiceCloneToLibrary(
				taskId, t2aResp.Data.AudioFile, req.TargetCategoryId, task.UserId,
			)
			if err != nil {
				global.GVA_LOG.Error("保存音频到音乐库失败", zap.Uint("taskId", taskId), zap.Error(err))
			} else {
				if musicUrl != "" {
					updateData["audio_url"] = musicUrl
				}
				if duration > 0 {
					updateData["duration"] = duration
				}
				if mediaId != "" {
					updateData["media_id"] = mediaId
				}
			}
		}
	}

	// 更新任务状态
	err = global.GVA_DB.Model(&ai.MinimaxVoiceCloneTask{}).Where("id = ?", taskId).Updates(updateData).Error
	if err != nil {
		global.GVA_LOG.Error("更新 MiniMax 任务状态失败", zap.Uint("taskId", taskId), zap.Error(err))
	}
}

// GetMinimaxVoiceCloneTaskStatus 获取 MiniMax 人声克隆任务状态
func (s *MinimaxVoiceCloneService) GetMinimaxVoiceCloneTaskStatus(taskId string) (*ai.MinimaxVoiceCloneTask, error) {
	var task ai.MinimaxVoiceCloneTask
	if err := global.GVA_DB.Where("task_id = ?", taskId).First(&task).Error; err != nil {
		return nil, err
	}
	return &task, nil
}

// GetMinimaxVoiceCloneTaskList 获取 MiniMax 人声克隆任务列表
func (s *MinimaxVoiceCloneService) GetMinimaxVoiceCloneTaskList(
	req aiRequest.MinimaxVoiceCloneTaskListRequest,
	userId uint,
) ([]ai.MinimaxVoiceCloneTask, int64, error) {
	var tasks []ai.MinimaxVoiceCloneTask
	var total int64

	db := global.GVA_DB.Model(&ai.MinimaxVoiceCloneTask{}).Where("user_id = ?", userId)

	// 统计总数
	db.Count(&total)

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	err := db.Order("created_at DESC").Offset(offset).Limit(req.PageSize).Find(&tasks).Error

	return tasks, total, err
}
