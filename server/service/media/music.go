package media

import (
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"os"
	"path"
	"strings"
	"time"

	"mime/multipart"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/media"
	mediaReq "github.com/flipped-aurora/gin-vue-admin/server/model/media/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	utilsAi "github.com/flipped-aurora/gin-vue-admin/server/utils/ai"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/ims"
	"go.uber.org/zap"
)

type MusicService struct{}

// UploadMusicFile 处理音乐文件上传
func (s *MusicService) UploadMusicFile(
	header *multipart.FileHeader,
	name string,
	categoryId,
	userId uint,
) (*media.Music, error) {
	var music media.Music
	music.CategoryId = categoryId

	if name == "" {
		name = strings.TrimSuffix(header.Filename, path.Ext(header.Filename))
	}
	music.Name = name

	// 获取文件后缀
	ext := path.Ext(header.Filename)
	music.Tag = strings.TrimPrefix(ext, ".")

	// 创建文件保存目录
	now := time.Now()
	uploadPath := global.GVA_CONFIG.Local.Path
	yearMonth := now.Format("2006-01")
	saveDir := path.Join(uploadPath, yearMonth)
	if err := os.MkdirAll(saveDir, os.ModePerm); err != nil {
		return nil, err
	}

	// 不使用name，生成随机名称，因为name可能很长导致oss上传报错
	randomName := utils.MD5V([]byte(fmt.Sprintf("%s_%d", time.Now().Format("20060102150405"), rand.Intn(1000))))

	// 生成文件名并保存文件
	fileName := randomName + ext
	filePath := path.Join(saveDir, fileName)

	if err := s.saveUploadedFile(header, filePath); err != nil {
		return nil, err
	}

	// 确保在函数退出时清理本地文件
	defer func() {
		if err := os.Remove(filePath); err != nil {
			global.GVA_LOG.Warn("删除本地文件失败", zap.String("filePath", filePath), zap.Error(err))
		}
	}()

	// 获取音频时长
	duration, err := utils.GetVideoDuration(filePath)
	if err != nil {
		global.GVA_LOG.Error("获取音频时长失败", zap.Error(err), zap.String("filePath", filePath))
	}
	music.Duration = duration

	// 检查是否需要上传到RunningHub (分类ID为42或其子分类)
	musicCategoryService := &MusicCategoryService{}
	isTargetCategory, err := musicCategoryService.IsTargetCategoryOrChild(categoryId, 42)
	if err != nil {
		global.GVA_LOG.Error("检查分类失败", zap.Error(err))
		return nil, fmt.Errorf("检查分类失败: %v", err)
	}

	if isTargetCategory {
		// 上传到RunningHub
		processor := utilsAi.NewRunningHubProcessor()
		uploadResponse, err := processor.UploadFileToRunningHub(header)
		if err != nil {
			global.GVA_LOG.Error("上传音乐到RunningHub失败", zap.Error(err))
			return nil, fmt.Errorf("上传音乐到RunningHub失败: %v", err)
		}

		if uploadResponse.Code != 0 {
			global.GVA_LOG.Error("RunningHub上传失败", zap.String("msg", uploadResponse.Msg))
			return nil, fmt.Errorf("RunningHub上传失败: %s", uploadResponse.Msg)
		}

		// 保存RunningHub返回的data数据
		runningHubDataBytes, err := json.Marshal(uploadResponse.Data)
		if err != nil {
			global.GVA_LOG.Error("序列化RunningHub data数据失败", zap.Error(err))
		} else {
			music.RunningHubData = string(runningHubDataBytes)
		}

		global.GVA_LOG.Info("音乐成功上传到RunningHub",
			zap.String("fileName", uploadResponse.Data.FileName),
			zap.String("fileType", uploadResponse.Data.FileType))
	}

	// 上传到阿里云VOD
	imsClient, err := ims.NewClient()
	if err != nil {
		return nil, err
	}

	uploadInfo, err := imsClient.UploadToVOD(imsClient, filePath, randomName, "audio")
	if err != nil {
		return nil, err
	}

	music.FileURL = uploadInfo.FileURL
	music.MediaId = uploadInfo.MediaId
	music.CreatorId = userId

	// 保存到数据库
	if err := global.GVA_DB.Create(&music).Error; err != nil {
		return nil, err
	}

	return &music, nil
}

// saveUploadedFile 保存上传的文件
func (s *MusicService) saveUploadedFile(header *multipart.FileHeader, dst string) error {
	src, err := header.Open()
	if err != nil {
		return err
	}
	defer src.Close()

	out, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer out.Close()

	_, err = io.Copy(out, src)
	return err
}

// GetMusicList 获取音乐列表
func (s *MusicService) GetMusicList(userId uint, info mediaReq.MusicSearch) (list interface{}, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := global.GVA_DB.Model(&media.Music{})
	var musics []struct {
		media.Music
		CreatorName string `json:"creatorName"` // 录入者名称
	}

	if info.CategoryId != 0 {
		db = db.Where("category_id = ?", info.CategoryId)
	} else {
		canViewCategoryIds, err := MusicCategoryServiceApp.GetUserCategoryIds(userId)
		if err != nil {
			return nil, 0, err
		}
		db = db.Where("category_id IN (?)", canViewCategoryIds)
	}

	if info.Keyword != "" {
		keywordLike := "%" + info.Keyword + "%"
		db = db.Where("name LIKE ?", keywordLike)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	err = db.Limit(limit).Offset(offset).
		Select("media_music.*, sys_users.nick_name as creator_name").
		Joins("left join sys_users on media_music.creator_id = sys_users.id").
		Order("media_music.updated_at desc").
		Find(&musics).Error

	return musics, total, err
}

// DeleteMusic 删除音乐
func (s *MusicService) DeleteMusic(id uint, userId uint) error {
	var music media.Music
	if err := global.GVA_DB.First(&music, id).Error; err != nil {
		return err
	}

	// 验证权限
	if music.CreatorId != userId {
		return fmt.Errorf("无权限删除此音乐")
	}

	return global.GVA_DB.Unscoped().Delete(&music).Error
}

// EditMusicName 编辑音乐名称
func (s *MusicService) EditMusicName(music *media.Music) error {
	return global.GVA_DB.Model(music).Where("id = ?", music.ID).Update("name", music.Name).Error
}

// EditMusicCategory 修改音乐分类
func (s *MusicService) EditMusicCategory(music *media.Music) error {
	return global.GVA_DB.Model(music).Where("id = ?", music.ID).Update("category_id", music.CategoryId).Error
}

// ImportMusicURLs 批量导入音乐URL
func (s *MusicService) ImportMusicURLs(musics []media.Music) error {
	return global.GVA_DB.Create(&musics).Error
}

func (s *MusicService) GetMusicByID(id uint) (music media.Music, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&music).Error
	return
}

// GetList 获取音频资源列表
func (s *MusicService) GetList(categoryId int, keyword string, page int, pageSize int) (list interface{}, total int64, err error) {
	limit := pageSize
	offset := pageSize * (page - 1)
	db := global.GVA_DB.Model(&media.Music{})
	var musics []struct {
		media.Music
		CreatorName string `json:"creatorName"` // 录入者名称
	}

	if keyword != "" {
		db = db.Where("name LIKE ?", "%"+keyword+"%")
	}

	if categoryId > 0 {
		db = db.Where("category_id = ?", categoryId)
	}

	err = db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = db.Limit(limit).Offset(offset).
		Select("media_music.*, sys_users.nick_name as creator_name").
		Joins("left join sys_users on media_music.creator_id = sys_users.id").
		Order("media_music.created_at DESC").
		Find(&musics).Error

	return musics, total, err
}

// SaveVoiceCloneToLibrary 将语音克隆任务生成的音频保存到音乐库
func (s *MusicService) SaveVoiceCloneToLibrary(taskId uint, audioUrl string, categoryId uint, userId uint) (musicUrl string, duration int64, mediaId string, err error) {
	global.GVA_LOG.Info("开始保存语音克隆音频到音乐库",
		zap.Uint("taskId", taskId),
		zap.String("audioUrl", audioUrl),
		zap.Uint("categoryId", categoryId),
		zap.Uint("userId", userId))

	// 获取语音克隆任务信息
	var voiceCloneTask struct {
		Text string `json:"text"`
	}
	err = global.GVA_DB.Table("ai_voice_clone_tasks").
		Select("text").
		Where("id = ?", taskId).
		First(&voiceCloneTask).Error
	if err != nil {
		global.GVA_LOG.Error("获取语音克隆任务信息失败", zap.Uint("taskId", taskId), zap.Error(err))
		return "", 0, "", fmt.Errorf("获取任务信息失败: %v", err)
	}

	global.GVA_LOG.Debug("获取到语音克隆任务信息", zap.Uint("taskId", taskId), zap.String("text", voiceCloneTask.Text))

	// 使用文本内容作为音乐名称，如果太长则截取前400个字符（保留UTF-8安全性）
	musicName := voiceCloneTask.Text
	if len([]rune(musicName)) > 400 {
		runes := []rune(musicName)
		musicName = string(runes[:400]) + "..."
	}

	// 检查是否已经保存过了（基于任务ID而不是名称）
	var existingMusic media.Music
	existingErr := global.GVA_DB.Where("name LIKE ? AND creator_id = ?", fmt.Sprintf("%%任务-%d%%", taskId), userId).First(&existingMusic).Error

	// 下载RunningHub的音频并上传到阿里云智能媒体服务
	global.GVA_LOG.Info("开始下载并上传音频", zap.Uint("taskId", taskId), zap.String("audioUrl", audioUrl))
	finalAudioUrl, finalMediaId, finalDuration, err2 := s.downloadAndUploadAudio(audioUrl, fmt.Sprintf("voice-clone-task-%d", taskId))
	if err2 != nil {
		global.GVA_LOG.Error("下载并上传音频失败", zap.Uint("taskId", taskId), zap.String("audioUrl", audioUrl), zap.Error(err2))
		return "", 0, "", fmt.Errorf("处理音频文件失败: %v", err2)
	}

	global.GVA_LOG.Info("音频处理成功",
		zap.Uint("taskId", taskId),
		zap.String("finalAudioUrl", finalAudioUrl),
		zap.String("finalMediaId", finalMediaId),
		zap.Int64("finalDuration", finalDuration))

	// 验证返回的数据
	if finalAudioUrl == "" {
		global.GVA_LOG.Error("音频处理返回的URL为空", zap.Uint("taskId", taskId))
		return "", 0, "", fmt.Errorf("音频处理返回的URL为空")
	}
	if finalMediaId == "" {
		global.GVA_LOG.Error("音频处理返回的MediaId为空", zap.Uint("taskId", taskId))
		return "", 0, "", fmt.Errorf("音频处理返回的MediaId为空")
	}
	if finalDuration <= 0 {
		global.GVA_LOG.Warn("音频处理返回的时长为0或负数", zap.Uint("taskId", taskId), zap.Int64("duration", finalDuration))
		// 时长为0不是致命错误，继续执行
	}

	if existingErr == nil {
		// 已存在，更新URL、MediaId、时长和名称
		err = global.GVA_DB.Model(&existingMusic).Updates(map[string]interface{}{
			"file_url": finalAudioUrl,
			"media_id": finalMediaId,
			"duration": finalDuration,
			"name":     musicName,
		}).Error
		if err != nil {
			return "", 0, "", err
		}
		return finalAudioUrl, finalDuration, finalMediaId, nil
	}

	// 创建新的音乐记录
	music := media.Music{
		CategoryId: categoryId,
		Name:       musicName,
		FileURL:    finalAudioUrl,
		MediaId:    finalMediaId,
		Duration:   finalDuration,
		CreatorId:  userId,
		Tag:        "flac", // 默认为flac格式
	}

	err = global.GVA_DB.Create(&music).Error
	if err != nil {
		return "", 0, "", err
	}

	return finalAudioUrl, finalDuration, finalMediaId, nil
}

// downloadAndUploadAudio 下载音频并上传到阿里云智能媒体服务
func (s *MusicService) downloadAndUploadAudio(audioUrl, fileName string) (string, string, int64, error) {
	global.GVA_LOG.Info("开始下载音频文件", zap.String("audioUrl", audioUrl), zap.String("fileName", fileName))

	// 1. 下载音频文件
	resp, err := http.Get(audioUrl)
	if err != nil {
		global.GVA_LOG.Error("HTTP请求失败", zap.String("audioUrl", audioUrl), zap.Error(err))
		return "", "", 0, fmt.Errorf("下载音频失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		global.GVA_LOG.Error("HTTP请求返回非200状态码", zap.String("audioUrl", audioUrl), zap.Int("statusCode", resp.StatusCode))
		return "", "", 0, fmt.Errorf("下载音频失败，状态码: %d", resp.StatusCode)
	}

	global.GVA_LOG.Debug("音频文件下载成功", zap.String("audioUrl", audioUrl), zap.Int64("contentLength", resp.ContentLength))

	// 2. 创建临时文件
	tempFile, err := os.CreateTemp("", fileName+"*.flac")
	if err != nil {
		return "", "", 0, fmt.Errorf("创建临时文件失败: %v", err)
	}
	tempPath := tempFile.Name()
	defer os.Remove(tempPath) // 确保删除临时文件

	// 3. 将音频内容写入临时文件
	_, err = io.Copy(tempFile, resp.Body)
	if err != nil {
		tempFile.Close()
		return "", "", 0, fmt.Errorf("写入音频内容失败: %v", err)
	}
	tempFile.Close()

	// 4. 获取音频时长
	global.GVA_LOG.Debug("开始获取音频时长", zap.String("tempPath", tempPath))
	duration, err := utils.GetVideoDuration(tempPath)
	if err != nil {
		global.GVA_LOG.Error("获取音频时长失败", zap.Error(err), zap.String("filePath", tempPath))
		duration = 0 // 设置默认值
	} else {
		global.GVA_LOG.Debug("成功获取音频时长", zap.Int64("duration", duration))
	}

	// 5. 上传到阿里云智能媒体服务
	global.GVA_LOG.Info("开始上传到阿里云", zap.String("fileName", fileName))
	imsClient, err := ims.NewClient()
	if err != nil {
		global.GVA_LOG.Error("创建阿里云客户端失败", zap.Error(err))
		return "", "", 0, fmt.Errorf("创建阿里云客户端失败: %v", err)
	}

	uploadInfo, err := imsClient.UploadToVOD(imsClient, tempPath, fileName, "audio")
	if err != nil {
		global.GVA_LOG.Error("上传到阿里云失败", zap.Error(err))
		return "", "", 0, fmt.Errorf("上传到阿里云失败: %v", err)
	}

	// 验证上传结果
	if uploadInfo.FileURL == "" {
		global.GVA_LOG.Error("上传成功但返回的FileURL为空", zap.String("fileName", fileName))
		return "", "", 0, fmt.Errorf("上传成功但返回的FileURL为空")
	}
	if uploadInfo.MediaId == "" {
		global.GVA_LOG.Error("上传成功但返回的MediaId为空", zap.String("fileName", fileName))
		return "", "", 0, fmt.Errorf("上传成功但返回的MediaId为空")
	}

	global.GVA_LOG.Info("成功上传语音克隆音频到阿里云",
		zap.String("fileName", fileName),
		zap.String("mediaId", uploadInfo.MediaId),
		zap.String("fileUrl", uploadInfo.FileURL),
		zap.Int64("duration", duration))

	return uploadInfo.FileURL, uploadInfo.MediaId, duration, nil
}

// SaveMinimaxVoiceCloneToLibrary 将 MiniMax 语音克隆任务生成的音频保存到音乐库
func (s *MusicService) SaveMinimaxVoiceCloneToLibrary(taskId uint, audioUrl string, categoryId uint, userId uint) (musicUrl string, duration int64, mediaId string, err error) {
	global.GVA_LOG.Info("开始保存 MiniMax 语音克隆音频到音乐库",
		zap.Uint("taskId", taskId),
		zap.String("audioUrl", audioUrl),
		zap.Uint("categoryId", categoryId),
		zap.Uint("userId", userId))

	// 获取 MiniMax 语音克隆任务信息
	var minimaxVoiceCloneTask struct {
		Text string `json:"text"`
	}
	err = global.GVA_DB.Table("ai_minimax_voice_clone_tasks").
		Select("text").
		Where("id = ?", taskId).
		First(&minimaxVoiceCloneTask).Error
	if err != nil {
		global.GVA_LOG.Error("获取 MiniMax 语音克隆任务信息失败", zap.Uint("taskId", taskId), zap.Error(err))
		return "", 0, "", fmt.Errorf("获取任务信息失败: %v", err)
	}

	global.GVA_LOG.Debug("获取到 MiniMax 语音克隆任务信息", zap.Uint("taskId", taskId), zap.String("text", minimaxVoiceCloneTask.Text))

	// 使用文本内容作为音乐名称，如果太长则截取前400个字符（保留UTF-8安全性）
	musicName := minimaxVoiceCloneTask.Text
	if len([]rune(musicName)) > 400 {
		runes := []rune(musicName)
		musicName = string(runes[:400]) + "..."
	}

	// 检查是否已经保存过了（基于任务ID而不是名称）
	var existingMusic media.Music
	existingErr := global.GVA_DB.Where("name LIKE ? AND creator_id = ?", fmt.Sprintf("%%MiniMax任务-%d%%", taskId), userId).First(&existingMusic).Error

	// 下载 MiniMax 的音频并上传到阿里云智能媒体服务
	global.GVA_LOG.Info("开始下载并上传 MiniMax 音频", zap.Uint("taskId", taskId), zap.String("audioUrl", audioUrl))
	finalAudioUrl, finalMediaId, finalDuration, err2 := s.downloadAndUploadAudio(audioUrl, fmt.Sprintf("minimax-voice-clone-task-%d", taskId))
	if err2 != nil {
		global.GVA_LOG.Error("下载并上传 MiniMax 音频失败", zap.Uint("taskId", taskId), zap.String("audioUrl", audioUrl), zap.Error(err2))
		return "", 0, "", fmt.Errorf("处理音频文件失败: %v", err2)
	}

	global.GVA_LOG.Info("MiniMax 音频处理成功",
		zap.Uint("taskId", taskId),
		zap.String("finalAudioUrl", finalAudioUrl),
		zap.String("finalMediaId", finalMediaId),
		zap.Int64("finalDuration", finalDuration))

	// 验证返回的数据
	if finalAudioUrl == "" {
		global.GVA_LOG.Error("MiniMax 音频处理返回的URL为空", zap.Uint("taskId", taskId))
		return "", 0, "", fmt.Errorf("音频处理返回的URL为空")
	}
	if finalMediaId == "" {
		global.GVA_LOG.Error("MiniMax 音频处理返回的MediaId为空", zap.Uint("taskId", taskId))
		return "", 0, "", fmt.Errorf("音频处理返回的MediaId为空")
	}
	if finalDuration <= 0 {
		global.GVA_LOG.Warn("MiniMax 音频处理返回的时长为0或负数", zap.Uint("taskId", taskId), zap.Int64("duration", finalDuration))
		// 时长为0不是致命错误，继续执行
	}

	if existingErr == nil {
		// 已存在，更新URL、MediaId、时长和名称
		err = global.GVA_DB.Model(&existingMusic).Updates(map[string]interface{}{
			"file_url": finalAudioUrl,
			"media_id": finalMediaId,
			"duration": finalDuration,
			"name":     musicName,
		}).Error
		if err != nil {
			return "", 0, "", err
		}
		return finalAudioUrl, finalDuration, finalMediaId, nil
	}

	// 创建新的音乐记录
	music := media.Music{
		CategoryId: categoryId,
		Name:       musicName,
		FileURL:    finalAudioUrl,
		MediaId:    finalMediaId,
		Duration:   finalDuration,
		CreatorId:  userId,
		Tag:        "mp3", // MiniMax 默认为mp3格式
	}

	err = global.GVA_DB.Create(&music).Error
	if err != nil {
		return "", 0, "", err
	}

	return finalAudioUrl, finalDuration, finalMediaId, nil
}
