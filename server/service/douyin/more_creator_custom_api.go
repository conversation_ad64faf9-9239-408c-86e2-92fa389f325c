package douyin

import (
	"fmt"
	"sync"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
)

/**
* MoreAPI自定义创作者中心:
 */
type MoreCreatorCustomApiService struct {
	client *DyHttpClient
	once   sync.Once
}

var MoreCreatorCustomApiServiceApp = new(MoreCreatorCustomApiService)

func (s *MoreCreatorCustomApiService) GetClient() *DyHttpClient {
	if s.client == nil {
		s.once.Do(func() {
			s.client = NewDyHttpClient(global.GVA_CONFIG.DouyinMoreAPI.CustomUrl)
		})
	}
	return s.client
}

// GetIncomeCategorySummary 获取收入分类汇总
func (s *MoreCreatorCustomApiService) GetIncomeCategorySummary(req request.MoreCreatorCustomApiIncomeCategorySummaryRequest) (resp response.MoreCreatorCustomApiIncomeCategorySummaryResponse, err error) {
	formData := map[string]string{}
	formData["cookie"] = req.Cookie
	if req.Proxy != "" {
		formData["proxy"] = utils.HttpProxy(req.Proxy)
	}
	if req.CategoryId != "" {
		formData["category_id"] = req.CategoryId
	}
	err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/income_category_summary", formData, &resp)
	if err != nil {
		return resp, err
	}
	if resp.Code != 200 {
		return resp, fmt.Errorf("获取收入分类汇总失败：code: %d,message:%s", resp.Code, resp.Msg)
	}

	return resp, nil
}

// PublishVideo 发布视频
func (s *MoreCreatorCustomApiService) CustomCreateVod(req request.MoreCreatorCustomApiCustomCreateVodRequest) (resp response.MoreCreatorCustomApiCustomCreateVodResponse, err error) {
	formData := map[string]string{}
	formData["cookie"] = req.Cookie
	formData["description"] = req.Description
	formData["visibility_type"] = req.VisibilityType
	if req.Proxy != "" {
		formData["proxy"] = utils.HttpProxy(req.Proxy)
	}
	if req.VideoVid != "" {
		formData["video_vid"] = req.VideoVid
	}
	if req.Download != "" {
		formData["download"] = req.Download
	}
	if req.Challenges != "" {
		formData["challenges"] = req.Challenges
	}
	if req.Timing != "" {
		formData["timing"] = req.Timing
	}
	if req.PoiName != "" {
		formData["poi_name"] = req.PoiName
	}
	if req.PoiId != "" {
		formData["poi_id"] = req.PoiId
	}
	if req.MusicId != "" {
		formData["music_id"] = req.MusicId
	}
	if req.MusicIdEndTime != "" {
		formData["music_end_time"] = req.MusicIdEndTime
	}

	remoteFiles := map[string]string{}
	if req.UploadPosterPath != "" {
		remoteFiles["upload_poster"] = req.UploadPosterPath
	}
	if req.VideoPath != "" {
		remoteFiles["video"] = req.VideoPath
	}

	err = s.GetClient().DoMultipartRequest("POST", "/api/douyin_v1/create_vod", formData, nil, remoteFiles, &resp)
	if err != nil {
		return resp, err
	}
	if resp.Status != 0 {
		return resp, fmt.Errorf("发布抖音视频失败：服务器报错code: %d,message:%s", resp.Status, resp.Message)
	}
	if resp.Code != 200 {
		return resp, fmt.Errorf("发布抖音视频失败：code: %d,message:%s", resp.Code, resp.Msg)
	}

	return resp, nil
}

// GetWorkList 获取作品列表
func (s *MoreCreatorCustomApiService) GetWorkList(req request.MoreCreatorCustomApiWorkListRequest) (resp response.MoreCreatorCustomApiWorkListResponse, err error) {
	formData := map[string]string{}
	formData["cookie"] = req.Cookie
	formData["count"] = fmt.Sprint(req.Count)
	formData["status"] = fmt.Sprint(req.Status)
	if req.Proxy != "" {
		formData["proxy"] = utils.HttpProxy(req.Proxy)
	}
	if req.MaxCursor != "" {
		formData["max_cursor"] = req.MaxCursor
	}

	// 发送请求
	err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/work_list", formData, &resp)
	if err != nil {
		return resp, err
	}
	if resp.Code != 200 {
		return resp, fmt.Errorf("请求抖音作品列表失败：code: %d,message:%s, msg: %s", resp.Code, resp.Message, resp.Msg)
	}

	return resp, nil
}

// 修改作品权限
func (s *MoreCreatorCustomApiService) UpdateWorkVisibility(req request.MoreCreatorCustomApiWorkModifyRequest) (resp response.MoreCreatorCustomApiCustomGenernalResponse, err error) {
	formData := map[string]string{}
	formData["cookie"] = req.Cookie
	formData["item_id"] = req.ItemId
	formData["visibility_type"] = fmt.Sprint(req.VisibilityType)
	if req.Download != 0 {
		formData["download"] = fmt.Sprint(req.Download)
	}
	if req.Proxy != "" {
		formData["proxy"] = utils.HttpProxy(req.Proxy)
	}

	// 发送请求
	err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/update_aweme", formData, &resp)
	if err != nil {
		return resp, err
	}
	if resp.Code != 200 {
		return resp, fmt.Errorf("请求修改抖音作品失败：code: %d,message:%s", resp.Code, resp.Msg)
	}

	return resp, nil
}

// 删除作品
func (s *MoreCreatorCustomApiService) DeleteWork(req request.MoreCreatorCustomApiWorkDeleteRequest) (resp response.MoreCreatorCustomApiCustomGenernalResponse, err error) {
	formData := map[string]string{}
	formData["cookie"] = req.Cookie
	formData["item_id"] = req.ItemId
	if req.Proxy != "" {
		formData["proxy"] = utils.HttpProxy(req.Proxy)
	}

	// 发送请求
	err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/delete_aweme", formData, &resp)
	if err != nil {
		return resp, err
	}
	if resp.Code != 200 {
		return resp, fmt.Errorf("请求删除抖音作品失败：code: %d,message:%s", resp.Code, resp.Msg)
	}

	if resp.Data.StatusCode != 0 {
		return resp, fmt.Errorf("删除抖音作品失败：status_code: %d,status_message:%s", resp.Data.StatusCode, resp.Data.StatusMessage)
	}

	return resp, nil
}
