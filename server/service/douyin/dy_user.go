package douyin

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type DyUserService struct{}

var DyUserServiceApp = new(DyUserService)

func (s *DyUserService) AddUser(dyUser *douyin.DyUser) error {
	var existingUser douyin.DyUser
	err := global.GVA_DB.Unscoped().Where("uid = ?", dyUser.UID).First(&existingUser).Error
	if err == nil {
		// 用户已存在(包括已删除的)
		if existingUser.DeletedAt.Valid {
			// 如果是已删除的记录，恢复并更新
			return global.GVA_DB.Unscoped().Model(&existingUser).Updates(map[string]interface{}{
				"deleted_at":         nil,
				"im_token":           dyUser.ImToken,
				"category_id":        dyUser.CategoryId,
				"nickname":           dyUser.Nickname,
				"avatar":             dyUser.Avatar,
				"unique_id":          dyUser.UniqueId,
				"short_id":           dyUser.ShortId,
				"sec_uid":            dyUser.SecUid,
				"follower_count":     dyUser.FollowerCount,
				"total_favorited":    dyUser.TotalFavorited,
				"account_region":     dyUser.AccountRegion,
				"province":           dyUser.Province,
				"city":               dyUser.City,
				"college_name":       dyUser.CollegeName,
				"bind_phone":         dyUser.BindPhone,
				"birthday":           dyUser.Birthday,
				"gender":             dyUser.Gender,
				"signature":          dyUser.Signature,
				"sys_user_id":        dyUser.SysUserId,
				"is_product_enabled": dyUser.IsProductEnabled,
			}).Error
		}
		// 如果是未删除的记录，直接更新
		return global.GVA_DB.Model(&existingUser).Updates(dyUser).Error
	}
	// 用户不存在,创建新用户
	err = global.GVA_DB.Create(dyUser).Error
	if err != nil {
		return err
	}

	// 设置当前用户的排序列表
	s.SetNewUserSort(dyUser)

	return nil
}

func (s *DyUserService) SetNewUserSort(dyUser *douyin.DyUser) error {
	if dyUser.SysUserId > 0 {
		sysUserId := uint(dyUser.SysUserId)
		// 获取全部分类的排序列表
		allCategoryUserIds, err := s.GetUserSort(sysUserId, nil)
		if err == nil {
			// 将新用户添加到列表首位
			newUserIds := append([]uint{dyUser.ID}, allCategoryUserIds...)
			// 保存更新后的排序
			err = s.SaveUserSort(sysUserId, nil, newUserIds)
			if err != nil {
				global.GVA_LOG.Error("保存全部分类排序失败", zap.Error(err))
			}
		}

		// 如果用户有分类，获取该分类的排序列表
		if dyUser.CategoryId > 0 {
			categoryId := uint(dyUser.CategoryId)
			categoryUserIds, err := s.GetUserSort(sysUserId, &categoryId)
			if err == nil {
				// 将新用户添加到列表首位
				newUserIds := append([]uint{dyUser.ID}, categoryUserIds...)
				// 保存更新后的排序
				err = s.SaveUserSort(sysUserId, &categoryId, newUserIds)
				if err != nil {
					global.GVA_LOG.Error("保存分类排序失败", zap.Error(err))
				}
			}
		}
	}
	return nil
}


// AddUserWithDevice 添加用户并绑定设备
func (s *DyUserService) AddUserWithDevice(dyUser *douyin.DyUser) error {
	return global.GVA_DB.Create(dyUser).Error
}

// GetUserList 获取抖音用户列表
func (s *DyUserService) GetUserList(info request.DyUserSearch, sysUserIds []uint) (list []douyin.DyUser, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := global.GVA_DB.Model(&douyin.DyUser{})

	// 构建查询条件
	if info.InvalidLogin == 1 {
		// 查询无效登录的用户
		db = db.Where("status = ?", 0)
	}

	if info.CategoryId != nil && *info.CategoryId != 0 {
		db = db.Where("category_id = ?", *info.CategoryId)
	}
	if info.Nickname != nil && *info.Nickname != "" {
		db = db.Where("nickname LIKE ?", "%"+*info.Nickname+"%")
	}
	if info.UniqueId != nil && *info.UniqueId != "" {
		db = db.Where("unique_id LIKE ?", "%"+*info.UniqueId+"%")
	}
	// 添加sys_user_id查询条件，只能查询当前用户及其下级用户的抖音用户
	db = db.Where("sys_user_id IN ?", sysUserIds)

	// 获取总数
	err = db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取列表，关联 sys_users 表以获取录入者信息
	var users []struct {
		douyin.DyUser
		SysUserName   string `json:"sysUserName"`   // 录入者名称
		PhoneUserName string `json:"phoneUserName"` // 手机号实名
		PhoneOperator string `json:"phoneOperator"` // 手机号运营商
	}

	// 尝试获取用户自定义排序
	var sortedUserIds []uint
	if info.CurrentUserID > 0 {
		sortedUserIds, _ = s.GetUserSort(info.CurrentUserID, info.CategoryId)
	}

	// 根据是否有自定义排序决定查询方式
	if len(sortedUserIds) > 0 {
		// 有自定义排序，先获取所有数据然后排序
		err = db.Select(
			"dy_user.*, sys_users.nick_name as sys_user_name, phone_balance.real_name as phone_user_name, phone_balance.operator_type as phone_operator").
			Joins("left join sys_users on dy_user.sys_user_id = sys_users.id").
			Joins("left join phone_balance on dy_user.bind_phone = phone_balance.phone_number AND phone_balance.deleted_at IS NULL").
			Order("dy_user.created_at desc").
			Find(&users).Error

		if err != nil {
			return nil, 0, err
		}

		// 创建ID到用户的映射
		userMap := make(map[uint]struct {
			douyin.DyUser
			SysUserName   string `json:"sysUserName"`
			PhoneUserName string `json:"phoneUserName"`
			PhoneOperator string `json:"phoneOperator"`
		})
		for _, user := range users {
			userMap[user.ID] = user
		}

		// 按自定义顺序重新排列
		var sortedUsers []struct {
			douyin.DyUser
			SysUserName   string `json:"sysUserName"`
			PhoneUserName string `json:"phoneUserName"`
			PhoneOperator string `json:"phoneOperator"`
		}

		// 先添加已排序的用户
		for _, id := range sortedUserIds {
			if user, exists := userMap[id]; exists {
				sortedUsers = append(sortedUsers, user)
				delete(userMap, id) // 删除已添加的用户
			}
		}

		// 再添加未排序的用户（按创建时间倒序）
		// 将已排序的用户ID转换为map，提高查找效率
		sortedUserMap := make(map[uint]bool, len(sortedUserIds))
		for _, id := range sortedUserIds {
			sortedUserMap[id] = true
		}

		// 直接从已排序的users切片中筛选剩余用户
		for _, user := range users {
			// 如果用户ID不在已排序列表中，则添加到剩余用户列表
			if !sortedUserMap[user.ID] {
				sortedUsers = append(sortedUsers, user)
			}
		}

		// 分页处理
		users = sortedUsers
		if offset < len(users) {
			end := offset + limit
			if end > len(users) {
				end = len(users)
			}
			users = users[offset:end]
		} else {
			users = []struct {
				douyin.DyUser
				SysUserName   string `json:"sysUserName"`
				PhoneUserName string `json:"phoneUserName"`
				PhoneOperator string `json:"phoneOperator"`
			}{}
		}
	} else {
		// 没有自定义排序，使用默认排序
		err = db.Select(
			"dy_user.*, sys_users.nick_name as sys_user_name, phone_balance.real_name as phone_user_name, phone_balance.operator_type as phone_operator").
			Joins("left join sys_users on dy_user.sys_user_id = sys_users.id").
			Joins("left join phone_balance on dy_user.bind_phone = phone_balance.phone_number AND phone_balance.deleted_at IS NULL").
			Limit(limit).Offset(offset).Order("dy_user.created_at desc").Find(&users).Error
	}

	// 将结果转换为 DyUser 切片
	list = make([]douyin.DyUser, len(users))
	for i, user := range users {
		list[i] = user.DyUser
		var sysUserName string
		if user.SysUserName != "" {
			sysUserName = user.SysUserName
		} else {
			sysUserName = "未实名"
		}
		list[i].SysUserName = sysUserName
		list[i].PhoneUserName = user.PhoneUserName
		var phoneOperator string
		switch user.PhoneOperator {
		case "mobile":
			phoneOperator = "中国移动"
		case "unicom":
			phoneOperator = "中国联通"
		case "telecom":
			phoneOperator = "中国电信"
		case "35internet":
			phoneOperator = "三五互联"
		default:
			phoneOperator = "未知"
		}
		list[i].PhoneOperator = phoneOperator
	}

	return list, total, err
}

func (s *DyUserService) GetAllUsers(categoryId int, sysUserIds []uint) (users []douyin.DyUser, err error) {
	db := global.GVA_DB.Model(&douyin.DyUser{})

	// 构建查询条件
	if categoryId != 0 {
		db = db.Where("category_id = ?", categoryId)
	}
	// 添加sys_user_id查询条件，只能查询当前用户及其下级用户的抖音用户
	db = db.Where("sys_user_id IN ?", sysUserIds)

	err = db.Select("dy_user.* ").
		Find(&users).Error

	return
}

// DeleteUser 删除抖音用户
func (s *DyUserService) DeleteUser(id uint) error {
	// 开启事务
	tx := global.GVA_DB.Begin()

	// 查询用户信息
	var user douyin.DyUser
	if err := tx.First(&user, id).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 如果用户绑定了设备，解除绑定
	if user.BindDevice != "" {
		// 解析设备信息
		var deviceInfo map[string]interface{}
		if err := json.Unmarshal([]byte(user.BindDevice), &deviceInfo); err != nil {
			tx.Rollback()
			return err
		}

		// 获取设备ID
		if deviceID, ok := deviceInfo["id"].(float64); ok {
			// 更新设备表，解除绑定
			if err := tx.Model(&douyin.DeviceInfo{}).
				Where("id = ?", uint(deviceID)).
				Update("dy_user_id", nil).Error; err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	// 删除用户
	if err := tx.Delete(&douyin.DyUser{}, "id = ?", id).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return err
	}

	// 如果用户绑定了IP，更新IP池中的用户计数
	if user.BindIP != "" {
		var ipInfo douyin.IpPool
		if err := global.GVA_DB.Where("ip = ?", user.BindIP).First(&ipInfo).Error; err == nil {
			// 更新IP池中的用户计数
			dyIPService := &DyIPService{}
			if err := dyIPService.UpdateUserCount(ipInfo.ID); err != nil {
				return err
			}
		}
	}

	// 删除用户的自动发布任务
	if err := global.GVA_DB.Where("dy_user_id =?", id).Delete(&creative.AutoPublishVideo{}).Error; err != nil {
		return err
	}

	return nil
}

// GetUserByToken 根据token获取抖音用户信息
func (s *DyUserService) GetUserByToken(token string) (*douyin.DyUser, error) {
	var user douyin.DyUser
	err := global.GVA_DB.Where("im_token = ?", token).First(&user).Error
	return &user, err
}

// ToggleProductEnabled 切换选品状态
func (s *DyUserService) ToggleProductEnabled(id uint, enabled bool) error {
	return global.GVA_DB.Model(&douyin.DyUser{}).Where("id = ?", id).Update("is_product_enabled", enabled).Error
}

// 增加一个绑定用户手机号

// UpdateUserIP 更新用户绑定IP
func (s *DyUserService) UpdateUserIP(id uint, bindIP string) error {
	// 开启事务
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 获取用户当前绑定的IP
		var user douyin.DyUser
		if err := tx.Where("id = ?", id).First(&user).Error; err != nil {
			return err
		}

		// 更新用户绑定的IP
		if err := tx.Model(&douyin.DyUser{}).Where("id = ?", id).Update("bind_ip", bindIP).Error; err != nil {
			return err
		}

		// 如果原来有绑定IP，更新原IP的用户计数
		if user.BindIP != "" {
			var oldIP douyin.IpPool
			if err := tx.Where("ip = ?", user.BindIP).First(&oldIP).Error; err == nil {
				// 更新原IP的用户计数
				dyIPService := &DyIPService{}
				if err := dyIPService.UpdateUserCount(oldIP.ID); err != nil {
					return err
				}
			}
		}

		// 如果新绑定了IP，更新新IP的用户计数
		if bindIP != "" {
			var newIP douyin.IpPool
			if err := tx.Where("ip = ?", bindIP).First(&newIP).Error; err == nil {
				// 更新新IP的用户计数
				dyIPService := &DyIPService{}
				if err := dyIPService.UpdateUserCount(newIP.ID); err != nil {
					return err
				}
			}
		}

		return nil
	})
}

// 绑定信息
func (s *DyUserService) BindInfo(id uint, realName string, phone string) error {
	if realName != "" {
		return global.GVA_DB.Model(&douyin.DyUser{}).Where("id = ?", id).Update("real_name", realName).Error
	}
	if phone != "" {
		// 验证手机号是否存在于 phone_balance 表中且未删除
		var count int64
		err := global.GVA_DB.Model(&douyin.PhoneBalance{}).
			Where("phone_number = ? AND deleted_at IS NULL", phone).
			Count(&count).Error
		if err != nil {
			return fmt.Errorf("验证手机号失败: %w", err)
		}
		if count == 0 {
			return fmt.Errorf("该手机号不在话费余额表中或已被删除")
		}

		// 验证手机号是否已被其他用户绑定
		var existingUser douyin.DyUser
		err = global.GVA_DB.Where("bind_phone = ? AND id != ?", phone, id).First(&existingUser).Error
		if err == nil {
			return fmt.Errorf("该手机号已被其他用户绑定")
		}

		return global.GVA_DB.Model(&douyin.DyUser{}).Where("id = ?", id).Update("bind_phone", phone).Error
	}
	return nil
}

// BindDevice 绑定设备到用户
func (s *DyUserService) BindDevice(userID uint, deviceID uint) error {
	// 获取设备信息
	var device douyin.DeviceInfo
	if err := global.GVA_DB.First(&device, deviceID).Error; err != nil {
		return err
	}

	// 更新用户的设备信息
	return global.GVA_DB.Model(&douyin.DyUser{}).Where("id = ?", userID).Updates(map[string]interface{}{
		"bind_device": true,
		"device_id":   deviceID,
	}).Error
}

// GetUserByID 根据ID获取用户信息
func (s *DyUserService) GetUserByID(id uint) (*douyin.DyUser, error) {
	var user douyin.DyUser
	err := global.GVA_DB.Where("id = ?", id).First(&user).Error
	return &user, err
}

func (s *DyUserService) GetUserByUniqueId(uniqueId string) (*douyin.DyUser, error) {
	var user douyin.DyUser
	err := global.GVA_DB.Where("unique_id = ?", uniqueId).First(&user).Error
	return &user, err
}

// UpdateUserInfo 更新用户信息
func (s *DyUserService) UpdateUserInfo(id uint, updateData map[string]interface{}) error {
	return global.GVA_DB.Model(&douyin.DyUser{}).Where("id = ?", id).Updates(updateData).Error
}

// GetAwemeCookie 获取用户抖音号cookie
func (s *DyUserService) GetAwemeCookie(uniqueId string) (string, error) {
	cookieKey := fmt.Sprintf(douyin.MoreApiCookieKey, uniqueId)
	cookie, err := global.GVA_REDIS.Get(context.Background(), cookieKey).Result()
	return cookie, err
}

// SetAwemeCookie 设置用户cookie
func (s *DyUserService) SetAwemeCookie(uniqueId, cookie string) error {
	cookieKey := fmt.Sprintf(douyin.MoreApiCookieKey, uniqueId)
	return global.GVA_REDIS.Set(context.Background(), cookieKey, cookie, -1).Err()
}

// 获取专门用于请求More API的Cookie
func (s *DyUserService) GetAwemeDefaultCookie() (string, error) {
	return s.GetAwemeCookie(douyin.MoreApiRequestUniqueId)
}

// UpdateCategory 更新用户分类
func (s *DyUserService) UpdateCategory(id uint, categoryId uint) error {
	return global.GVA_DB.Model(&douyin.DyUser{}).Where("id = ?", id).Update("category_id", categoryId).Error
}

// UpdateUserRemark 更新用户备注
func (s *DyUserService) UpdateUserRemark(req request.UpdateUserRemarkRequest) error {
	return global.GVA_DB.Model(&douyin.DyUser{}).Where("id = ?", req.ID).Update("remark", req.Remark).Error
}

// SearchUserByUniqueId 根据抖音号搜索用户
func (s *DyUserService) SearchUserByUniqueId(uniqueId string) (list *[]douyin.DyUser, err error) {
	db := global.GVA_DB.Model(&douyin.DyUser{})
	// 添加模糊查询条件和未删除条件
	db = db.Where("unique_id LIKE ? AND deleted_at IS NULL", uniqueId+"%")
	// 获取符合条件的记录
	err = db.Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s *DyUserService) SearchUserByPort(port string) (list *[]douyin.DyUser, err error) {
	db := global.GVA_DB.Model(&douyin.DyUser{})
	// 添加模糊查询条件和未删除条件
	db = db.Where("bind_ip LIKE ? AND deleted_at IS NULL", "%"+port)
	// 获取符合条件的记录
	err = db.Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s *DyUserService) SearchUserByMac(mac string) (list *[]douyin.IpPool, err error) {
	db := global.GVA_DB.Model(&douyin.IpPool{})
	// 添加模糊查询条件
	db = db.Where("mac_addresses LIKE ?", mac+"%")
	// 获取符合条件的记录
	err = db.Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

// SaveUserSort 保存用户排序到Redis
func (s *DyUserService) SaveUserSort(sysUserId uint, categoryId *uint, userIds []uint) error {
	// 构建Redis key，按用户和分类存储
	key := fmt.Sprintf("user_sort:%d", sysUserId)
	if categoryId != nil && *categoryId > 0 {
		key = fmt.Sprintf("%s:category:%d", key, *categoryId)
	} else {
		key = fmt.Sprintf("%s:category:0", key)
	}

	// 将用户ID列表转换为JSON字符串存储
	userIdsJson, err := json.Marshal(userIds)
	if err != nil {
		return err
	}

	// 存储到Redis，设置过期时间为30天
	return global.GVA_REDIS.Set(context.Background(), key, string(userIdsJson), 30*24*time.Hour).Err()
}

// GetUserSort 从Redis获取用户排序
func (s *DyUserService) GetUserSort(sysUserId uint, categoryId *uint) ([]uint, error) {
	// 构建Redis key
	key := fmt.Sprintf("user_sort:%d", sysUserId)
	if categoryId != nil && *categoryId > 0 {
		key = fmt.Sprintf("%s:category:%d", key, *categoryId)
	} else {
		key = fmt.Sprintf("%s:category:0", key)
	}

	// 从Redis获取数据
	result, err := global.GVA_REDIS.Get(context.Background(), key).Result()
	if err != nil {
		// 如果没有找到排序数据，返回空数组
		if err.Error() == "redis: nil" {
			return []uint{}, nil
		}
		return nil, err
	}

	// 解析JSON数据
	var userIds []uint
	if err := json.Unmarshal([]byte(result), &userIds); err != nil {
		return nil, err
	}

	return userIds, nil
}
