package douyin

import (
	"fmt"
	"sync"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
)

/**
* MoreAPI创作者中心
 */
type MoreCreatorApiService struct {
	client *DyHttpClient
	once   sync.Once
}

var MoreCreatorApiServiceApp = new(MoreCreatorApiService)

func (s *MoreCreatorApiService) GetClient() *DyHttpClient {
	if s.client == nil {
		s.once.Do(func() {
			s.client = NewDyHttpClient(global.GVA_CONFIG.DouyinMoreAPI.CreatorUrl)
		})
	}
	return s.client
}

func (s *MoreCreatorApiService) GetQRCode(req request.MoreCreatorApiGetQRCodeRequest) (resp response.MoreCreatorApiGetQRCodeResponse, err error) {
	formData := map[string]string{}
	if req.Proxy != "" {
		formData["proxy"] = utils.HttpProxy(req.Proxy)
	}
	if req.Did != "" {
		formData["did"] = req.Did
	}
	if req.Iid != "" {
		formData["iid"] = req.Iid
	}
	err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/get_qrcode", formData, &resp)
	if err != nil {
		return resp, err
	}
	if resp.Code != 200 {
		return resp, fmt.Errorf("获取二维码GetQRCode失败：code: %d,message:%s", resp.Code, resp.Msg)
	}

	return resp, nil
}

func (s *MoreCreatorApiService) CheckQRCode(req request.MoreCreatorApiCheckQRCodeRequest) (resp response.MoreCreatorApiCheckQRCodeResponse, err error) {
	formData := map[string]string{
		"token": req.Token,
	}
	if req.Proxy != "" {
		formData["proxy"] = utils.HttpProxy(req.Proxy)
	}
	err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/check_qrcode", formData, &resp)
	if err != nil {
		return resp, err
	}
	if resp.Code != 200 {
		return resp, fmt.Errorf("请求检查二维码接口失败：code: %d,message:%s", resp.Code, resp.Msg)
	}

	return resp, nil
}

// 企业号设备注册
func (s *MoreCreatorApiService) DeviceRegister() (resp response.MoreCreatorApiDeviceRegisterResponse, err error) {
	err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/device_register", nil, &resp)
	if err != nil {
		return resp, err
	}
	if resp.Code != 200 {
		return resp, fmt.Errorf("请求发送验证码失败：code: %d,message:%s", resp.Code, resp.Msg)
	}

	return resp, nil
}

func (s *MoreCreatorApiService) SendCaptcha(req request.MoreCreatorApiSendCaptchaRequest) (resp response.MoreCreatorApiCheckCaptchaResponse, err error) {
	formData := map[string]string{
		"token":       req.Token,
		"encrypt_uid": req.EncryptUid,
	}
	if req.Proxy != "" {
		formData["proxy"] = utils.HttpProxy(req.Proxy)
	}
	if req.EncryptOperStaffUid != "" {
		formData["encrypt_oper_staff_uid"] = req.EncryptOperStaffUid
	}

	fmt.Printf("发送短信参数：%+v", formData)
	err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/send_sms", formData, &resp)
	if err != nil {
		return resp, err
	}
	if resp.Code != 200 {
		return resp, fmt.Errorf("请求发送验证码失败：code: %d,message:%s", resp.Code, resp.Msg)
	}

	return resp, nil
}

func (s *MoreCreatorApiService) ValidCaptcha(req request.MoreCreatorApiValidCaptchaRequest) (resp response.MoreCreatorApiValidCaptchaResponse, err error) {
	formData := map[string]string{
		"token":       req.Token,
		"encrypt_uid": req.EncryptUid,
		"code":        req.Code,
	}
	if req.Proxy != "" {
		formData["proxy"] = utils.HttpProxy(req.Proxy)
	}
	if req.EncryptOperStaffUid != "" {
		formData["encrypt_oper_staff_uid"] = req.EncryptOperStaffUid
	}

	err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/verify_sms", formData, &resp)
	if err != nil {
		return resp, err
	}
	if resp.Code != 200 {
		return resp, fmt.Errorf("请求校验验证码失败：code: %d,message:%s", resp.Code, resp.Msg)
	}
	if resp.Data.Data.Description != "" {
		return resp, fmt.Errorf("请求校验验证码失败：description:%s", resp.Data.Data.Description)
	}

	return resp, nil
}

// 添加 GetLoginUserInfo 方法
func (s *MoreCreatorApiService) GetLoginUserInfo(req request.MoreCreatorApiVGetLoginUserInfoRequest) (resp response.MoreCreatorApiVGetLoginUserInfoResponse, err error) {
	formData := map[string]string{
		"cookie": req.Cookie,
	}
	if req.Proxy != "" {
		formData["proxy"] = utils.HttpProxy(req.Proxy)
	}

	err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/creator_info", formData, &resp)
	if err != nil {
		resp.CookieCode = 2
		return resp, fmt.Errorf("请求报错：code: %d,message:%s, err:%s", resp.Code, resp.Msg, err.Error())
	}

	if resp.Code == 0 && resp.Msg == "" {
		resp.Code = 512
		resp.Msg = "获取登录用户信息失败"
		resp.CookieCode = 2
		return resp, fmt.Errorf("请求超时：code: %d,message:%s", resp.Code, resp.Msg)
	}

	if resp.Code != 200 {
		resp.CookieCode = 2
		return resp, fmt.Errorf("获取登录用户信息失败：code: %d,message:%s", resp.Code, resp.Msg)
	}

	if resp.Data.StatusCode != 0 {
		resp.CookieCode = 1
		return resp, fmt.Errorf("获取登录用户信息失败：status_code: %d,status_msg:%s", resp.Data.StatusCode, resp.Data.StatusMsg)
	}

	return resp, nil
}

// VodUpload 异步上传发布视频
func (s *MoreCreatorApiService) VodUpload(
	req request.MoreCreatorApiVodUploadRequest,
) (resp response.MoreCreatorApiVodUploadResponse, err error) {
	formData := map[string]string{
		"cookie": req.Cookie,
	}

	remoteFiles := map[string]string{
		"video": req.VideoPath,
	}

	err = s.GetClient().DoMultipartRequest("POST", "/api/douyin_v1/vod_upload", formData, nil, remoteFiles, &resp)
	if err != nil {
		return resp, err
	}

	if resp.Code != 200 {
		return resp, fmt.Errorf("异步上传视频失败：code: %d, message:%s", resp.Code, resp.Msg)
	}

	return resp, nil
}

// GetVodUploadResult 获取异步上传视频结果
func (s *MoreCreatorApiService) GetVodUploadResult(
	req request.MoreCreatorApiGetVodUploadResultRequest,
) (resp response.MoreCreatorApiGetVodUploadResultResponse, err error) {
	formData := map[string]string{
		"task_id": req.TaskId,
	}
	if req.Proxy != "" {
		formData["proxy"] = utils.HttpProxy(req.Proxy)
	}

	err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/vod_upload_result", formData, &resp)
	if err != nil {
		return resp, err
	}

	if resp.Code != 200 {
		return resp, fmt.Errorf("获取上传结果失败：code: %d, message:%s", resp.Code, resp.Msg)
	}

	return resp, nil
}
