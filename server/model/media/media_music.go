package media

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// Music 音乐文件结构
type Music struct {
	global.GVA_MODEL
	Name           string `json:"name" gorm:"type:varchar(500);comment:音乐名称"`                  // 音乐名称
	FileURL        string `json:"fileUrl" gorm:"comment:音乐链接"`                                 // 音乐链接
	Tag            string `json:"tag" gorm:"comment:文件类型"`                                     // 文件类型
	MediaId        string `json:"mediaId" gorm:"comment:媒资ID"`                                 // 媒资ID
	CategoryId     uint   `json:"categoryId" gorm:"comment:分类ID;index"`                        // 分类ID
	CreatorId      uint   `json:"creatorId" gorm:"comment:录入者ID"`                              // 录入者ID
	Duration       int64  `json:"duration" gorm:"comment:音乐时长;default:0"`                      // 音乐时长(秒)
	RunningHubData string `json:"runningHubData" gorm:"type:text;comment:RunningHub返回的data数据"` // RunningHub返回的data数据(JSON格式)
	VoiceId        string `json:"voiceId" gorm:"comment:MiniMax音色ID"`                          // MiniMax音色ID，用于人声克隆
}

// TableName 指定表名
func (m *Music) TableName() string {
	return "media_music"
}
