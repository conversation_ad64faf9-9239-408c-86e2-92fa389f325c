package request

/**
* MoreCreatorAPI请求类
 */

type MoreCreatorApiGetQRCodeRequest struct {
	Did   string `json:"did"`
	Iid   string `json:"iid"`
	Proxy string `json:"proxy"`
}

type MoreCreatorApiCheckQRCodeRequest struct {
	Token string `json:"token"`
	Proxy string `json:"proxy"`
}

type MoreCreatorApiSendCaptchaRequest struct {
	Token               string `json:"token"`
	Proxy               string `json:"proxy"`
	EncryptUid          string `json:"encrypt_uid"`
	EncryptOperStaffUid string `json:"encrypt_oper_staff_uid"`
}

type MoreCreatorApiValidCaptchaRequest struct {
	Code                string `json:"code"`
	Token               string `json:"token"`
	Proxy               string `json:"proxy"`
	EncryptUid          string `json:"encrypt_uid"`
	EncryptOperStaffUid string `json:"encrypt_oper_staff_uid"`
}

type MoreCreatorApiVGetLoginUserInfoRequest struct {
	Cookie string `json:"cookie"`
	Proxy  string `json:"proxy"`
}

// MoreCreatorApiSearchMusicRequest 搜索音乐请求
type MoreCreatorApiSearchMusicRequest struct {
	Keyword string `json:"keyword" form:"keyword" binding:"required"` // 关键词
	Cursor  string `json:"cursor" form:"cursor" binding:"required"`   // 下一页锚点
	Count   string `json:"count" form:"count" binding:"required"`     // 每页条数
	Proxy   string `json:"proxy,omitempty" form:"proxy"`              // 代理
	Cookie  string `json:"cookie,omitempty" form:"cookie"`            // 用户cookie
}

type MoreCreatorApiVodUploadRequest struct {
	Cookie    string `json:"cookie"`     // 登录接口获取到的cookie
	VideoPath string `json:"video_path"` // 视频文件路径
}

// MoreCreatorApiGetVodUploadResultRequest 获取异步上传视频结果请求
type MoreCreatorApiGetVodUploadResultRequest struct {
	TaskId string `json:"task_id"` // 上传任务ID
	Proxy  string `json:"proxy"`
}
