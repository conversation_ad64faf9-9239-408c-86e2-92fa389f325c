package request

// MinimaxVoiceCloneTaskRequest MiniMax人声克隆任务请求
type MinimaxVoiceCloneTaskRequest struct {
	Text               string  `json:"text" binding:"required"`                                    // 要转换的文本
	Model              string  `json:"model"`                                                      // 模型，默认speech-01
	Speed              float64 `json:"speed"`                                                      // 语速，默认1.0
	Volume             float64 `json:"volume"`                                                     // 音量，默认1.0
	Pitch              float64 `json:"pitch"`                                                      // 音调，默认0.0
	AudioSampleRate    int     `json:"audioSampleRate"`                                            // 音频采样率，默认32000
	BitRate            int     `json:"bitRate"`                                                    // 比特率，默认128000
	Format             string  `json:"format"`                                                     // 音频格式，默认mp3
	TargetCategoryId   uint    `json:"targetCategoryId" binding:"required"`                       // 目标分类ID
	ReferenceAudioUrl  string  `json:"referenceAudioUrl" binding:"required"`                      // 参考音频文件URL
	ReferenceAudioName string  `json:"referenceAudioName"`                                        // 参考音频文件名称
}

// MinimaxVoiceCloneTaskListRequest MiniMax人声克隆任务列表请求
type MinimaxVoiceCloneTaskListRequest struct {
	Page     int `json:"page" form:"page"`         // 页码
	PageSize int `json:"pageSize" form:"pageSize"` // 每页数量
}
